package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @CreatedOn 25/06/25 12:04
 * @UpdatedBy martinspectre
 * @UpdatedOn 25/06/25 12:04
 */
@Service
public class NCBSCWorkflowService {
    private final WorkflowClient workflowClient;
    private final NCBSCApplicationService applicationService;
    private final Logger logger = LoggerFactory.getLogger(NCBSCWorkflowService.class);

    public NCBSCWorkflowService(WorkflowClient workflowClient, NCBSCApplicationService applicationService) {
        this.workflowClient = workflowClient;
        this.applicationService = applicationService;
    }

    @Async("workflowExecutor")
    public void processWorkflowInBackground(NCBSCApplication recognitionApplication) {
        // Handle auto-assignment for applications in IN_PROCESSING state (after POP upload)
        if (recognitionApplication.getApplicationState().equals(Enums.State.IN_PROCESSING)) {
            try {
                // Auto-assign agent using AutoAssignService
                String assignedAgent = applicationService.assignAgentByWorkload(
                        Enums.ApplicationType.RECOGNITION.name(),
                        Enums.UserRoles.AGENT.name()
                );

                if (assignedAgent != null && !assignedAgent.trim().isEmpty()) {
                    applicationService.updateApplicationAssignedUser(
                            recognitionApplication.getReferenceNumber(),
                            Enums.UserRoles.AGENT,
                            assignedAgent
                    );
                    logger.info("Auto-assigned agent {} to NCBSC application {}", assignedAgent, recognitionApplication.getReferenceNumber());
                } else {
                    logger.warn("No agent available for auto-assignment to NCBSC application {}", recognitionApplication.getReferenceNumber());
                }
            } catch (Exception e) {
                logger.error("Failed to auto-assign agent to NCBSC application {}: {}", recognitionApplication.getReferenceNumber(), e.getMessage());
            }
        }

        // Handle workflow process for submitted applications
        if (recognitionApplication.getApplicationState().equals(Enums.State.SUBMITTED)) {
            try {
                Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                        Enums.ApplicationType.RECOGNITION.name(),
                        recognitionApplication.getReferenceNumber()
                );

                logger.info("Workflow response: {}", workflowResponse);

                if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                    Object data = workflowResponse.get("applicationData");
                    if (data != null) {
                        String processInstanceId = (String) workflowResponse.get("processInstanceId");
                        String referenceId = recognitionApplication.getReferenceNumber();
                        applicationService.updateProcessInstanceId(referenceId, processInstanceId);
                        logger.info("Process instance ID {} saved for application {}", processInstanceId, referenceId);
                    }
                }
            } catch (Exception e) {
                logger.error("Failed to start workflow process: {}", e.getMessage(), e);
            }
        }
    }
}
