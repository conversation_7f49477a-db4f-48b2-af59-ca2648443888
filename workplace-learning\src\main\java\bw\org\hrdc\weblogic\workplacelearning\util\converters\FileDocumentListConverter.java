package bw.org.hrdc.weblogic.workplacelearning.util.converters;

import bw.org.hrdc.weblogic.workplacelearning.entity.document.FileDocument;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

import java.util.List;

/**
 * <AUTHOR>
 * @CreatedOn 03/07/2025 21:40
 */
@Converter
public class FileDocumentListConverter implements AttributeConverter<List<FileDocument>, String> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(List<FileDocument> attribute) {
        try {
            return attribute == null ? "[]" : objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize FileDocument list", e);
        }
    }

    @Override
    public List<FileDocument> convertToEntityAttribute(String dbData) {
        try {
            return dbData == null || dbData.isBlank()
                    ? List.of()
                    : objectMapper.readValue(dbData, objectMapper.getTypeFactory().constructCollectionType(List.class, FileDocument.class));
        } catch (Exception e) {
            throw new RuntimeException("Failed to deserialize FileDocument list", e);
        }
    }
}