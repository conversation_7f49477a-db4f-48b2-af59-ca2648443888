package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * NOC Workflow Service for handling auto-assignment and workflow initiation
 * Similar to NCBSCWorkflowService
 */
@Service
public class NOCWorkflowService {
    private final WorkflowClient workflowClient;
    private final NOCApplicationService applicationService;
    private final Logger logger = LoggerFactory.getLogger(NOCWorkflowService.class);

    public NOCWorkflowService(WorkflowClient workflowClient, NOCApplicationService applicationService) {
        this.workflowClient = workflowClient;
        this.applicationService = applicationService;
    }

    @Async("workflowExecutor")
    public void processWorkflowInBackground(NOCApplication nocApplication) {
        // Handle both auto-assignment and workflow process for applications in IN_PROCESSING state (after POP upload)
        if (nocApplication.getApplicationState().equals(Enums.State.IN_PROCESSING)) {
            
            // Step 1: Auto-assign agent using AutoAssignService
            try {
                String assignedAgent = applicationService.assignAgentByWorkload(
                        Enums.ApplicationType.NOC.name(),
                        Enums.UserRoles.AGENT.name()
                );

                if (assignedAgent != null && !assignedAgent.trim().isEmpty()) {
                    applicationService.updateApplicationAssignedUser(
                            nocApplication.getReferenceNumber(),
                            Enums.UserRoles.AGENT,
                            assignedAgent
                    );
                    logger.info("Auto-assigned agent {} to NOC application {}", assignedAgent, nocApplication.getReferenceNumber());
                } else {
                    logger.warn("No agent available for auto-assignment to NOC application {}", nocApplication.getReferenceNumber());
                }
            } catch (Exception e) {
                logger.error("Failed to auto-assign agent to NOC application {}: {}", nocApplication.getReferenceNumber(), e.getMessage());
            }

            // Step 2: Start workflow process
            try {
                Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                        Enums.ApplicationType.NOC.name(),
                        nocApplication.getReferenceNumber()
                );

                logger.info("Workflow response: {}", workflowResponse);

                if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                    Object data = workflowResponse.get("applicationData");
                    if (data != null) {
                        String processInstanceId = (String) workflowResponse.get("processInstanceId");
                        String referenceId = nocApplication.getReferenceNumber();
                        applicationService.updateProcessInstanceId(referenceId, processInstanceId);
                        logger.info("Process instance ID {} saved for application {}", processInstanceId, referenceId);
                    }
                }
            } catch (Exception e) {
                logger.error("Failed to start workflow process: {}", e.getMessage(), e);
            }
        }
    }
}
