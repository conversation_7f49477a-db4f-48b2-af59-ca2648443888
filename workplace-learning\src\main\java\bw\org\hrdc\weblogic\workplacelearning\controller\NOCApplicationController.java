package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ApplicationStatusUpdatePayload;
import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.NOCDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.responsedto.NOCApplicationCustomDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.common.Quotation;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.DocumentCommon;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.service.common.QuotationService;
import bw.org.hrdc.weblogic.workplacelearning.service.document.DocumentService;
import bw.org.hrdc.weblogic.workplacelearning.service.document.FileDocumentService;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NCBSCApplicationCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NCBSCApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NOCApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.ReferenceNumberGenerator;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 11:11
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 11:11
 */
@RestController
@RequestMapping("/api/v1/ncbsc/noc")
public class NOCApplicationController {

    private static final Logger logger = LoggerFactory.getLogger(NOCApplicationController.class);

    @Autowired
    private NOCApplicationService service;

    @Autowired
    private NCBSCApplicationCommentsService commentsService;

    @Autowired
    private NCBSCApplicationService NCBSCService;

    @PersistenceContext
    EntityManager entityManager;
    @Autowired
    private QuotationService quotationService;

    @Autowired
    private WorkflowClient workflowClient;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private FileDocumentService fileDocumentService;

    @PostMapping("/")
    @Transactional
    public ResponseEntity<?> create(@RequestBody NOCDto fullApplication) {
        logger.info("NOC Application creation initiated");
        try {
            logger.info("Checking ongoing application for recognition id {}", fullApplication.getRecognitionNumber());
            if(service.isDuplicateChangeRequest(fullApplication.getRecognitionNumber())){
                logger.error("Application already exist for recognition application id {}", fullApplication.getRecognitionNumber());
                return ApiResponse.createErrorResponse("APPLICATION_EXIST", "Duplicate application, there is already an existing application in progress.");
            }
            // extracting the entity for minimum disruptions
            NOCApplication application = service.fullApplicationToEntity(fullApplication);

            application.setDateSubmitted(LocalDateTime.now());
            String applicationNumber = ReferenceNumberGenerator.generateReferenceNumber("app");
            application.setApplicationNumber(applicationNumber);

            Map<String, Object> response = new HashMap<>();

            if(application.getApplicationState().equals(Enums.State.SUBMITTED)){
                application.setApplicationState(Enums.State.PAYMENT);
                application.setApplicationStatus(Enums.Status.PENDING);

                Optional<NCBSCApplication> original = NCBSCService.getApplicationByReference(application.getRecognitionNumber());
                if(original.isPresent()){
                    Map<String, Object> quotationObject = quotationService.calculateNocQuotation(original.get(), application);
                    boolean isMajorChanges = (boolean) quotationObject.get("hasMajorChange");
                    application.setIsMajorChange(isMajorChanges);
                    if(!isMajorChanges){
                        String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                        application.setReferenceNumber(referenceNumber);
                        application.setApplicationState(Enums.State.SUBMITTED);
                    }

                    NOCApplication nocApplication = service.create(application);

                    if (nocApplication == null) {
                        logger.error("New NOC application for company id {} failed to create", application.getOrganisationId());
                        return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create NOC application.");
                    }

                    response.put("nocApplication", nocApplication);

                    //Do quotation for the new NOC
                    if(isMajorChanges){
                        Quotation quotation = quotationService.save(original.get(), nocApplication);
                        if(quotation != null){
                            response.put("quotation", quotation);
                        }
                    }
                }else{
                    return ResponseEntity.ok(new ApiResponse<>(false, "Recognition details provided are invalid", null, null));
                }
            }else{
                NOCApplication nocApplication = service.create(application);

                if (nocApplication == null) {
                    logger.error("New NOC application for company id {} failed to create", application.getOrganisationId());
                    return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create NOC application.");
                }

                response.put("nocApplication", nocApplication);
            }

            if(fullApplication.getAttachments() != null && application.getUuid() != null){
                logger.info("Saved Application get Uuid {}", application.getUuid());
                fullApplication.setAttachments(fileDocumentService.saveDocuments(application.getUuid(), Enums.FileEntityType.NOC_APPLICATION, fullApplication.getAttachments()));
                response.put("nocApplication", service.fullLApplicationToDto(application,fullApplication.getAttachments()));
            }

            logger.info("New NOC application for company id {} created successfully", application.getOrganisationId());

            return ResponseEntity.ok(new ApiResponse<>(true, "NOC Application created successfully", response, null));
        }catch (Exception exception){
            logger.error("NOC Application for company id {} failed with exception: {}", fullApplication.getOrganisationId(), exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

    @GetMapping("/")
    public ResponseEntity<?> getAll(@RequestParam int offset, @RequestParam int pageSize, @RequestParam(required = false) String assignedTo, @RequestParam(required = false) String state) {
        try{
            if (offset < 0 )
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }
            Page<Map<String, Object>> applications = service.getAllApplications(PageRequest.of(offset, pageSize), assignedTo, state);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));

        }catch (Exception exception){
            logger.error("Error Fetching NOC Applications exception: {}", exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

    @GetMapping("/company/{organisationId}/by-organisation/{page}/{size}")
    public ResponseEntity<?> getByOrganisationId(@PathVariable String organisationId,@PathVariable int page,@PathVariable int size) {
        try{
            if (page < 0) {
                page = 0;
            }
            if(size <= 0) {
                size = 10;
            }

            Page<?> applications = service.getByOrganisationId(organisationId, page, size);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));

        } catch (Exception e) {
            logger.error("Error Fetching NOC Applications for organisationId {} exception: {}", organisationId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/reference-number/{referenceNumber}")
    @Transactional
    public ResponseEntity<ApiResponse<?>> searchByReferenceNumber(@PathVariable String referenceNumber) {
        try {

            Optional<NOCApplication> application = service.getApplicationByReference(referenceNumber);
            Map<String, Object> applicationResponse = new HashMap<>(Map.of());
            if(application.isPresent()){

                applicationResponse.put("scopeOfAccreditations", application.get().getScopeOfAccreditation());
                applicationResponse.put("learningOutcomes", application.get().getCourseContentAndDelivery().getLearningOutcomes());

                NOCApplicationCustomDto finalApplicationList = application.stream().map(app -> {
                    NOCApplicationCustomDto dto = new NOCApplicationCustomDto();
                    dto.setAssignedAgent(app.getAssignedAgent());
                    dto.setAssignedAgentLead(app.getAssignedAgentLead());
                    dto.setAssignedOfficerLead(app.getAssignedOfficerLead());
                    dto.setAssignedOfficer(app.getAssignedOfficer());
                    dto.setAssignedManager(app.getAssignedManager());
                    dto.setReferenceNumber(app.getReferenceNumber());
                    dto.setApplicationNumber(String.valueOf(app.getApplicationNumber()));
                    dto.setJustification(app.getJustification());
                    dto.setRecognitionNumber(app.getRecognitionNumber());
                    dto.setApplicationState(String.valueOf(app.getApplicationState()));
                    dto.setOrganisationId(app.getOrganisationId());
                    dto.setTrainingNeedsAssessmentPurpose(app.getTrainingNeedsAssessmentPurpose());
                    dto.setTrainingNeedsAssessmentSkillsNeedsAnalysis(app.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
                    dto.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
                    dto.setKeyFacilitation(app.getKeyFacilitation());
                    dto.setAssessmentType(app.getAssessmentType());
                    dto.setCertification(app.getCertification());
                    dto.setThirdPartyArrangements(app.getThirdPartyArrangements());
                    dto.setResources(app.getResources());
                    dto.setShortCourseEndorsement(app.getShortCourseEndorsement());
                    dto.setDateSubmitted(app.getDateSubmitted());
                    dto.setApplicationStatus(app.getApplicationStatus().name());
                    dto.setShortCourseInformation(app.getShortCourseInformation());
                    dto.setCourseContentAndDelivery(app.getCourseContentAndDelivery());
                    dto.setId(app.getId());
                    dto.setUuid(app.getUuid());
                    dto.setCreatedAt(app.getCreatedAt());
                    dto.setUpdatedAt(app.getUpdatedAt());
                    dto.setCreatedBy(app.getCreatedBy());
                    dto.setUpdatedBy(app.getUpdatedBy());
                    dto.setDeleted(app.isDeleted());
                    dto.setProcessInstanceId(app.getProcessInstanceId());
                    return dto;
                }).toList().get(0);
                applicationResponse.put("application", finalApplicationList);

                List<NCBSCApplicationComments> applicationComments = commentsService.getAuditLogsForApplication(application.get().getUuid());
                if(!applicationComments.isEmpty()){
                    applicationResponse.put("comments", applicationComments);
                }

                Optional<Quotation> quotation = quotationService.getByReference(finalApplicationList.getUuid());
                if(quotation.isPresent()){
                    applicationResponse.put("quotation", quotation);
                }

                List<DocumentCommon> documents = documentService.fetchDocuments(finalApplicationList.getUuid());
                applicationResponse.put("documents", documents);

                List<FileDocumentDto> attachments = fileDocumentService.getAttachmentsByEntityIdAndType(UUID.fromString(application.get().getUuid()), Enums.FileEntityType.NOC_APPLICATION);
                applicationResponse.put("attachments", attachments);


                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", applicationResponse, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK).body(new ApiResponse<>(false, "No record found", null, null));
            }
        }catch (Exception exception){
            return ResponseEntity.status(HttpStatus.OK).body(new ApiResponse<>(false, "No record found", null, null));
        }
    }

    @GetMapping("/application-number/{applicationNumber}")
    @Transactional
    public ResponseEntity<ApiResponse<?>> searchByApplicationNumber(@PathVariable String applicationNumber) {
        try {
            Optional<NOCApplication> application = service.getByApplicationNumber(applicationNumber);
            Map<String, Object> applicationResponse = new HashMap<>(Map.of());
            System.out.println("application.isPresent() " + application.isPresent());
            if(application.isPresent()){
                applicationResponse.put("scopeOfAccreditations", application.get().getScopeOfAccreditation());
                applicationResponse.put("learningOutcomes", application.get().getCourseContentAndDelivery().getLearningOutcomes());

                NOCApplicationCustomDto finalApplicationList = application.stream().map(app -> {
                    NOCApplicationCustomDto dto = new NOCApplicationCustomDto();
                    dto.setAssignedAgent(app.getAssignedAgent());
                    dto.setAssignedAgentLead(app.getAssignedAgentLead());
                    dto.setAssignedOfficerLead(app.getAssignedOfficerLead());
                    dto.setAssignedOfficer(app.getAssignedOfficer());
                    dto.setAssignedManager(app.getAssignedManager());
                    dto.setReferenceNumber(app.getReferenceNumber());
                    dto.setApplicationNumber(String.valueOf(app.getApplicationNumber()));
                    dto.setJustification(app.getJustification());
                    dto.setRecognitionNumber(app.getRecognitionNumber());
                    dto.setApplicationState(String.valueOf(app.getApplicationState()));
                    dto.setOrganisationId(app.getOrganisationId());
                    dto.setTrainingNeedsAssessmentPurpose(app.getTrainingNeedsAssessmentPurpose());
                    dto.setTrainingNeedsAssessmentSkillsNeedsAnalysis(app.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
                    dto.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
                    dto.setKeyFacilitation(app.getKeyFacilitation());
                    dto.setAssessmentType(app.getAssessmentType());
                    dto.setCertification(app.getCertification());
                    dto.setThirdPartyArrangements(app.getThirdPartyArrangements());
                    dto.setResources(app.getResources());
                    dto.setShortCourseEndorsement(app.getShortCourseEndorsement());
                    dto.setDateSubmitted(app.getDateSubmitted());
                    dto.setApplicationStatus(app.getApplicationStatus().name());
                    dto.setShortCourseInformation(app.getShortCourseInformation());
                    dto.setCourseContentAndDelivery(app.getCourseContentAndDelivery());
                    dto.setId(app.getId());
                    dto.setUuid(app.getUuid());
                    dto.setCreatedAt(app.getCreatedAt());
                    dto.setUpdatedAt(app.getUpdatedAt());
                    dto.setCreatedBy(app.getCreatedBy());
                    dto.setUpdatedBy(app.getUpdatedBy());
                    dto.setDeleted(app.isDeleted());
                    return dto;
                }).toList().get(0);
                applicationResponse.put("application", finalApplicationList);

                List<NCBSCApplicationComments> applicationComments = commentsService.getAuditLogsForApplication(application.get().getUuid());
                if(!applicationComments.isEmpty()){
                    applicationResponse.put("comments", applicationComments);
                }

                Optional<Quotation> quotation = quotationService.getByReference(finalApplicationList.getUuid());
                if(quotation.isPresent()){
                    applicationResponse.put("quotation", quotation);
                }

                List<DocumentCommon> documents = documentService.fetchDocuments(finalApplicationList.getUuid());
                applicationResponse.put("documents", documents);

                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", applicationResponse, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));

            }
        }catch (Exception exception){
            logger.error("Internal server error fetching noc, Exception", exception);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ApiResponse<>(false, "Internal server error", null, null));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable String id) {
        Optional<NOCApplication> application = service.getById(id);
        Map<String, Object> applicationResponse = new HashMap<>(Map.of());
        System.out.println("application.isPresent() " + application.isPresent());
        if(application.isPresent()){
            applicationResponse.put("scopeOfAccreditations", application.get().getScopeOfAccreditation());
            applicationResponse.put("learningOutcomes", application.get().getCourseContentAndDelivery().getLearningOutcomes());

            NOCApplicationCustomDto finalApplicationList = application.stream().map(app -> {
                NOCApplicationCustomDto dto = new NOCApplicationCustomDto();
                dto.setAssignedAgent(app.getAssignedAgent());
                dto.setAssignedAgentLead(app.getAssignedAgentLead());
                dto.setAssignedOfficerLead(app.getAssignedOfficerLead());
                dto.setAssignedOfficer(app.getAssignedOfficer());
                dto.setAssignedManager(app.getAssignedManager());
                dto.setReferenceNumber(app.getReferenceNumber());
                dto.setApplicationNumber(String.valueOf(app.getApplicationNumber()));
                dto.setJustification(app.getJustification());
                dto.setRecognitionNumber(app.getRecognitionNumber());
                dto.setApplicationState(String.valueOf(app.getApplicationState()));
                dto.setOrganisationId(app.getOrganisationId());
                dto.setTrainingNeedsAssessmentPurpose(app.getTrainingNeedsAssessmentPurpose());
                dto.setTrainingNeedsAssessmentSkillsNeedsAnalysis(app.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
                dto.setShortCourseDeliveryMode(app.getShortCourseDeliveryMode());
                dto.setKeyFacilitation(app.getKeyFacilitation());
                dto.setAssessmentType(app.getAssessmentType());
                dto.setCertification(app.getCertification());
                dto.setThirdPartyArrangements(app.getThirdPartyArrangements());
                dto.setResources(app.getResources());
                dto.setShortCourseEndorsement(app.getShortCourseEndorsement());
                dto.setDateSubmitted(app.getDateSubmitted());
                dto.setApplicationStatus(app.getApplicationStatus().name());
                dto.setShortCourseInformation(app.getShortCourseInformation());
                dto.setCourseContentAndDelivery(app.getCourseContentAndDelivery());
                dto.setId(app.getId());
                dto.setUuid(app.getUuid());
                dto.setCreatedAt(app.getCreatedAt());
                dto.setUpdatedAt(app.getUpdatedAt());
                dto.setCreatedBy(app.getCreatedBy());
                dto.setUpdatedBy(app.getUpdatedBy());
                dto.setDeleted(app.isDeleted());
                return dto;
            }).toList().get(0);
            applicationResponse.put("application", finalApplicationList);

            List<NCBSCApplicationComments> applicationComments = commentsService.getAuditLogsForApplication(application.get().getUuid());
            if(!applicationComments.isEmpty()){
                applicationResponse.put("comments", applicationComments);
            }

            Optional<Quotation> quotation = quotationService.getByReference(finalApplicationList.getUuid());
            if(quotation.isPresent()){
                applicationResponse.put("quotation", quotation);
            }

            List<DocumentCommon> documents = documentService.fetchDocuments(finalApplicationList.getUuid());
            applicationResponse.put("documents", documents);

            return ResponseEntity.ok(new ApiResponse<>(true, "Record found", applicationResponse, null));

        }else {
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(false, "No record found", null, null));

        }
    }

    @PutMapping("/update/{id}")
    @Transactional
    public ResponseEntity<?> update(@PathVariable String id, @RequestBody NOCApplication updated) {
        try {
            if (id == null){
                return ResponseEntity.ok(new ApiResponse<>(false, "application id is required", null, null));
            }

            if (updated == null){
                return ResponseEntity.ok(new ApiResponse<>(false, "application is required", null, null));
            }
            Map<String, Object> response = new HashMap<>();

            if(updated.getApplicationState().equals(Enums.State.SUBMITTED) && updated.getApplicationStatus().equals(Enums.Status.PENDING)){
                //Check if application has quote
                Optional<Quotation> quoteApplication = quotationService.getByReference(updated.getUuid());
                if(quoteApplication.isEmpty()){
                    Optional<NCBSCApplication> original = NCBSCService.getApplicationByReference(updated.getRecognitionNumber());
                    if(original.isPresent()){
                        Map<String, Object> quotationObject = quotationService.calculateNocQuotation(original.get(), updated);
                        boolean isMajorChanges = (boolean) quotationObject.get("hasMajorChange");
                        updated.setIsMajorChange(isMajorChanges);
                        if(!isMajorChanges){
                            String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                            updated.setReferenceNumber(referenceNumber);
                            updated.setApplicationState(Enums.State.SUBMITTED);
                        }

                        Optional<NOCApplication> nocApplication = service.update(id, updated);

                        if (nocApplication.isEmpty()) {
                            logger.error("NOC application for company id {} failed to update", updated.getOrganisationId());
                            return ResponseEntity.ok(new ApiResponse<>(false, "Record could not be updated", null, null));
                        }

                        response.put("nocApplication", nocApplication);

                        logger.info("NOC application for company id {} updated successfully", updated.getOrganisationId());
                        //Do quotation for the new NOC
                        if(isMajorChanges){
                            Quotation quotation = quotationService.save(original.get(), nocApplication.get());
                            if(quotation != null){
                                response.put("quotation", quotation);
                            }
                        }
                        return ResponseEntity.ok(new ApiResponse<>(true, "NOC Application updated successfully", response, null));
                    }
                }
            }
            Optional<NOCApplication> result = service.update(id, updated);
            if (result.isEmpty()){
                return ResponseEntity.ok(new ApiResponse<>(false, "Record could not be updated", null, null));
            }
            response.put("nocApplication", result.get());
            return ResponseEntity.ok(new ApiResponse<>(true, "Record Updated successfully", response, null));
        }catch (Exception exception){
            return getInternalServerError(exception.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable String id) {
        boolean deleted = service.delete(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    @PutMapping("/{applicationReference}/assign-user")
    public ResponseEntity<?> assignAgent(@PathVariable String applicationReference, @RequestBody Map<String, Object> payload) {
        logger.info("Application user assignment initiated for application reference : {}", applicationReference);
        try {
            String role = payload.get("role").toString();
            String userId = payload.get("userId").toString();

            if (role.isEmpty() || userId.isEmpty()) {
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", "Required fields are missing, please provide user id and their respective role");
            }

            Enums.UserRoles userRole = Enums.UserRoles.valueOf(role.toUpperCase());

            boolean updated = service.updateApplicationAssignedUser(applicationReference, userRole, userId);

            if (!updated) {
                return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application or application not found.");
            }

            // TODO: Trigger notification to agent for the assignment made
            return ResponseEntity.ok(new ApiResponse<>(true, "Application assigned successfully", null, null));

        } catch (Exception e) {
            logger.error("Failed to assign NOC application reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationReference}/status-update")
    public ResponseEntity<?> changeApplicationStatus(@PathVariable String applicationReference, @RequestBody ApplicationStatusUpdatePayload payload) {
        logger.info("NOC Application status update initiated for application reference : {}", applicationReference);
        try {
            String role = payload.getRole();
            Enums.UserRoles userRole;

            try {
                userRole = Enums.UserRoles.valueOf(role.toUpperCase());
            } catch (IllegalArgumentException e) {
                logger.error("Invalid role provided for application reference {}", applicationReference);
                return ResponseEntity.badRequest().body("Invalid role: " + role);
            }

            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getUserId();
            String actionType = null;
            if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                actionType = "Agent_action";
            }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                actionType = "Officer_action";
            }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)) {
                actionType = "Manager_action";
            }

            if (action.isEmpty() || userId.isEmpty()) {
                return ApiResponse.createErrorResponse("MANDATORY_FIELDS_MISSING", "Required fields are missing, please provide user id and their respective role");
            }

            Optional<NOCApplication> applicationOpt = service.getApplicationByReference(applicationReference);

            if (applicationOpt.isEmpty()) {
                return ApiResponse.createErrorResponse("NOT_FOUND", "Application not found with reference: " + applicationReference);
            }

            NOCApplication application = applicationOpt.get();

            boolean updated = service.updateApplicationStatus(application, userRole, action, newAssignee);

            if (!updated) {
                return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
            }

            // Auto-assign next role after successful status update (following NCBSC pattern)
            try {
                String autoAssignRole = null;
                if ("AGENT".equalsIgnoreCase(role) && "APPROVED".equalsIgnoreCase(action)) {
                    autoAssignRole = "OFFICER";
                } else if ("OFFICER".equalsIgnoreCase(role) && "APPROVED".equalsIgnoreCase(action)) {
                    autoAssignRole = "MANAGER";
                }

                if (autoAssignRole != null) {
                    logger.info("Triggering auto-assignment for role: {} after {} approval", autoAssignRole, role);
                    service.updateAutoassignedAgent(application.getUuid().toString(), autoAssignRole);
                    logger.info("Successfully completed auto-assignment for role: {}", autoAssignRole);
                }
            } catch (Exception e) {
                logger.error("Failed to auto-assign next role after {} approval: {}", role, e.getMessage(), e);
                // Don't fail the main operation if auto-assignment fails
            }

            entityManager.clear();

            NCBSCApplicationComments logEntry = new NCBSCApplicationComments();
            logEntry.setNocApplication(application);
            logEntry.setAction(action);
            logEntry.setComments(NCBSCService.sanitizeHtml(comments != null ? comments : "")); // prevent null
            logEntry.setUpdatedBy(userId);
            logEntry.setTimestamp(LocalDateTime.now());

            commentsService.createComments(logEntry);

            // Check if processInstanceId exists before using it
                    String processInstanceId = application.getProcessInstanceId();
                    logger.info("processInstanceId : {}", processInstanceId);
                    if (processInstanceId != null && !processInstanceId.isEmpty()) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("referenceNumber", applicationReference);
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.NOC.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, applicationReference);
                            workflowClient.resumeProcess(processInstanceId, actionType, workflowPayload);
                        } catch (Exception e) {
                            // Log the error but continue with the application status update
                            logger.error("Failed to resume workflow process: {}", e.getMessage());
                        }
                    } else {
                        logger.warn("No process instance ID found for application {}", applicationReference);
                    }

            return ResponseEntity.ok(new ApiResponse<>(true, "Application assigned successfully", null, null));

        } catch (Exception e) {
            logger.error("Failed to update status of NOC application reference {} with exception: {}", applicationReference, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{applicationId}/quotation-accept/{quotationId}/{status}")
    @Transactional
    public ResponseEntity<?> acceptQuotation(@PathVariable String applicationId, @PathVariable String quotationId, @PathVariable boolean status) {
        logger.info("NOC Application quote accept status initiated");
        try {
            Optional<NOCApplication> applicationObj = service.getById(applicationId);

            if (applicationObj.isEmpty()) {
                return ApiResponse.createErrorResponse("NOT_FOUND", "Application not found with id: " + applicationId);
            }
            NOCApplication application = applicationObj.get();
            if(status){
                String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                application.setReferenceNumber(referenceNumber);
                application.setApplicationState(Enums.State.SUBMITTED);
                application.setApplicationStatus(Enums.Status.PENDING_PAYMENT);

                NOCApplication nocApplication = service.create(application);

                if (nocApplication == null) {
                    logger.error("Failed to update NOC application for company id {} when accepting quotation", application.getOrganisationId());
                    return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to Accept the quotation.");
                }

                logger.info("Application for company id {} updated successfully, reference number generated and application submitted", application.getOrganisationId());
                //Update Quotation
                Map<String, Object> response = new HashMap<>();
                response.put("nocApplication", nocApplication);

                Quotation quotation = quotationService.acceptQuote(quotationId, true);
                if(quotation != null){
                    logger.info("Quotation for company id {} and application reference number {} has been updated successfully", application.getOrganisationId(), applicationId);
                    response.put("quotation", quotation);
                }else{
                    logger.error("Quotation for company id {} and application reference number {} failed to update", application.getOrganisationId(), applicationId);
                }
                return ResponseEntity.ok(new ApiResponse<>(true, "Quotation has been accepted successfully", response, null));
            }else{
                //Quotation not accepted, remove the quotation and set NOC application to draft
                quotationService.deleteById(quotationId);
                
                application.setApplicationState(Enums.State.DRAFT);
                application.setApplicationStatus(Enums.Status.PENDING);
                service.create(application);

                return ResponseEntity.ok(new ApiResponse<>(true, "Quotation has been rejected", null, null));
            }
        }catch (Exception exception){
            logger.error("Quotation status update for Application id {} failed with exception: {}", applicationId, exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

    @PutMapping("/{applicationId}/payment/upload")
    @Transactional
    public ResponseEntity<?> uploadingPOP(@PathVariable String applicationId, @RequestBody DocumentCommon popDocument) {
        logger.info("NOC Application pop submission initiated");
        try {
            Optional<NOCApplication> applicationObj = service.getById(applicationId);
            Map<String, Object> response = new HashMap<>();
            if (applicationObj.isEmpty()) {
                return ApiResponse.createErrorResponse("NOT_FOUND", "Application not found with id: " + applicationId);
            }
            NOCApplication application = applicationObj.get();
            if(!popDocument.getKey().isEmpty()){
                String referenceNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                application.setReferenceNumber(referenceNumber);
                application.setApplicationState(Enums.State.IN_PROCESSING);
                application.setApplicationStatus(Enums.Status.PENDING);

                NOCApplication nocApplication = service.create(application);

                if (nocApplication == null) {
                    logger.error("Failed to update NOC application for company id {} when uploading pop", application.getOrganisationId());
                    return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to upload pop.");
                }

                logger.info("Application for company id {} updated successfully, reference number generated and application submitted", application.getOrganisationId());
                //Update pop
                popDocument.setIdentifier(nocApplication.getUuid());

                DocumentCommon documentSaved = documentService.saveDocument(popDocument);

                if(documentSaved != null){
                    logger.info("POP document for company id {} and application reference number {} has been updated successfully", application.getOrganisationId(), applicationId);
                    List<DocumentCommon> documents = documentService.fetchDocuments(nocApplication.getUuid());
                    response.put("documents", documents);
                }else{
                    logger.error("POP Document for company id {} and application reference number {} failed to update", application.getOrganisationId(), applicationId);
                }
                response.put("nocApplication", nocApplication);

                // Auto-assign agent using AutoAssignService
                try {
                    logger.info("Starting auto-assignment process for NOC application after POP upload");
                    String assignedAgent = service.assignAgentByWorkload(
                            Enums.ApplicationType.NOC.name(),
                            Enums.UserRoles.AGENT.name()
                    );

                    if (assignedAgent != null && !assignedAgent.trim().isEmpty()) {
                        logger.info("Found agent {} for assignment, updating NOC application {}", assignedAgent, nocApplication.getReferenceNumber());
                        service.updateApplicationAssignedUser(
                                nocApplication.getReferenceNumber(),
                                Enums.UserRoles.AGENT,
                                assignedAgent
                        );
                        logger.info("Successfully auto-assigned agent {} to NOC application {}", assignedAgent, nocApplication.getReferenceNumber());
                    } else {
                        logger.warn("No agent available for auto-assignment to NOC application {}", nocApplication.getReferenceNumber());
                    }
                } catch (Exception e) {
                    logger.error("Failed to auto-assign agent to NOC application {}: {}", nocApplication.getReferenceNumber(), e.getMessage(), e);
                }

                if (nocApplication.getApplicationState().equals(Enums.State.IN_PROCESSING)) {
                    try {
                        logger.info("Starting workflow process for NOC application with reference number: {}", nocApplication.getReferenceNumber());

                        // Make sure the application data is available before calling the workflow service
                        // Commit the current transaction to ensure data is available to the workflow service
                        entityManager.flush();

                        // Generate a reference number for the application if not already set
                        final NOCApplication finalNocApplication;
                        if (nocApplication.getReferenceNumber() == null || nocApplication.getReferenceNumber().isEmpty()) {
                            String generatedRefNumber = ReferenceNumberGenerator.generateReferenceNumber("ref");
                            nocApplication.setReferenceNumber(generatedRefNumber);
                            finalNocApplication = service.create(nocApplication);
                            logger.info("Generated reference number {} for application", generatedRefNumber);
                        } else {
                            finalNocApplication = nocApplication;
                        }

                        // Start the workflow process in a separate thread to avoid blocking the response
                        new Thread(() -> {
                            try {
                                // Wait a bit to ensure the transaction is fully committed
                                Thread.sleep(2000);

                                // Add a bit more delay if the pre-check failed
                                Thread.sleep(1000);

                                // Now try to start the actual workflow process
                                logger.info("Initiating workflow process for application reference: {}", finalNocApplication.getReferenceNumber());

                                // Use a try-catch block specifically for the workflow call
                                try {
                                    Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(
                                            Enums.ApplicationType.NOC.name(),
                                            finalNocApplication.getReferenceNumber()
                                    );

                                    logger.info("Workflow response received: status={}, message={}",
                                            workflowResponse != null ? workflowResponse.get("status") : "null",
                                            workflowResponse != null ? workflowResponse.get("message") : "null");

                                    // Log the response structure for debugging
                                    if (workflowResponse != null) {
                                        logger.info("Workflow response keys: {}", workflowResponse.keySet());
                                        if (workflowResponse.containsKey("data")) {
                                            Object dataObj = workflowResponse.get("data");
                                            if (dataObj instanceof Map) {
                                                Map<String, Object> dataMap = (Map<String, Object>) dataObj;
                                                logger.info("Workflow response data keys: {}", dataMap.keySet());
                                            }
                                        }
                                    }

                                    // Check if the response contains a processInstanceId, which indicates success
                                    // regardless of the success flag value
                                    if (workflowResponse != null) {
                                        // Extract the process instance ID from the response
                                        String processInstanceId = null;
                                        String message = (String) workflowResponse.get("message");

                                        // First check if processInstanceId is directly in the response
                                        if (workflowResponse.containsKey("processInstanceId")) {
                                            processInstanceId = (String) workflowResponse.get("processInstanceId");
                                            logger.info("Found process instance ID directly in response: {}", processInstanceId);
                                        }
                                        // If not found, check in data structure
                                        else if (workflowResponse.containsKey("data") && workflowResponse.get("data") instanceof Map) {
                                            Map<String, Object> dataMap = (Map<String, Object>) workflowResponse.get("data");
                                            if (dataMap.containsKey("processInstanceId")) {
                                                processInstanceId = (String) dataMap.get("processInstanceId");
                                                logger.info("Found process instance ID in data.processInstanceId: {}", processInstanceId);
                                            }
                                        }

                                        // Consider it a success if we have a processInstanceId or if the message indicates success
                                        boolean isSuccess = Boolean.TRUE.equals(workflowResponse.get("success")) ||
                                                processInstanceId != null ||
                                                (message != null && message.contains("successfully"));

                                        if (isSuccess) {
                                            // If we have a processInstanceId, save it
                                            if (processInstanceId != null) {
                                                // Save the processInstanceId to the database
                                                service.updateProcessInstanceId(finalNocApplication.getReferenceNumber(), processInstanceId);
                                                logger.info("Process instance ID {} saved for application {}",
                                                        processInstanceId, finalNocApplication.getReferenceNumber());
                                            } else {
                                                logger.info("Workflow process started successfully but no process instance ID was found in the expected location");
                                                // Log the response structure to help with debugging
                                                logger.info("Response structure: {}", workflowResponse.keySet());
                                            }
                                        } else {
                                            String errorMessage = message != null ? message : "No response message from workflow service";
                                            logger.error("Workflow process failed to start: {}", errorMessage);
                                        }
                                    }
                                } catch (Exception e) {
                                    logger.error("Failed to start workflow process: {}", e.getMessage(), e);

                                    // If the workflow start fails, we'll try again after a longer delay
                                    try {
                                        logger.info("Retrying workflow process start after delay for application: {}",
                                                finalNocApplication.getReferenceNumber());
                                        Thread.sleep(5000);

                                        Map<String, Object> retryResponse = workflowClient.getNCBSCApplication(
                                                Enums.ApplicationType.NOC.name(),
                                                finalNocApplication.getReferenceNumber()
                                        );

                                        logger.info("Retry workflow response received: status={}, message={}",
                                                retryResponse != null ? retryResponse.get("status") : "null",
                                                retryResponse != null ? retryResponse.get("message") : "null");

                                        // Log the response structure for debugging
                                        if (retryResponse != null) {
                                            logger.info("Retry workflow response keys: {}", retryResponse.keySet());
                                            if (retryResponse.containsKey("data")) {
                                                Object dataObj = retryResponse.get("data");
                                                if (dataObj instanceof Map) {
                                                    Map<String, Object> dataMap = (Map<String, Object>) dataObj;
                                                    logger.info("Retry workflow response data keys: {}", dataMap.keySet());
                                                }
                                            }
                                        }

                                        if (retryResponse != null) {
                                            // Extract the process instance ID from the response
                                            String processInstanceId = null;
                                            String message = (String) retryResponse.get("message");

                                            // First check if processInstanceId is directly in the response
                                            if (retryResponse.containsKey("processInstanceId")) {
                                                processInstanceId = (String) retryResponse.get("processInstanceId");
                                                logger.info("Found process instance ID directly in retry response: {}", processInstanceId);
                                            }
                                            // If not found, check in data structure
                                            else if (retryResponse.containsKey("data") && retryResponse.get("data") instanceof Map) {
                                                Map<String, Object> dataMap = (Map<String, Object>) retryResponse.get("data");
                                                if (dataMap.containsKey("processInstanceId")) {
                                                    processInstanceId = (String) dataMap.get("processInstanceId");
                                                    logger.info("Found process instance ID in retry data.processInstanceId: {}", processInstanceId);
                                                }
                                            }

                                            // Consider it a success if we have a processInstanceId or if the message indicates success
                                            boolean isSuccess = Boolean.TRUE.equals(retryResponse.get("success")) ||
                                                    processInstanceId != null ||
                                                    (message != null && message.contains("successfully"));

                                            if (isSuccess) {
                                                logger.info("Retry workflow start succeeded for application: {}",
                                                        finalNocApplication.getReferenceNumber());

                                                if (processInstanceId != null) {
                                                    service.updateProcessInstanceId(finalNocApplication.getReferenceNumber(), processInstanceId);
                                                    logger.info("Process instance ID {} saved for application {}",
                                                            processInstanceId, finalNocApplication.getReferenceNumber());
                                                } else {
                                                    logger.info("Retry workflow process started successfully but no process instance ID was found in the expected location");
                                                    // Log the response structure to help with debugging
                                                    logger.info("Retry response structure: {}", retryResponse.keySet());
                                                }
                                            } else {
                                                logger.error("Retry workflow start also failed for application: {}",
                                                        finalNocApplication.getReferenceNumber());
                                            }
                                        } else {
                                            logger.error("No response received from workflow service during retry");
                                        }
                                    } catch (Exception retryEx) {
                                        logger.error("Workflow process retry also failed: {}", retryEx.getMessage(), retryEx);
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Failed in workflow background thread: {}", e.getMessage(), e);
                            }
                        }).start();

                        logger.info("Workflow process initiated in background for application: {}", finalNocApplication.getReferenceNumber());
                    } catch (Exception e) {
                        logger.error("Failed to prepare workflow process: {}", e.getMessage(), e);
                        // Continue with the response - we don't want to fail the quotation acceptance just because workflow failed
                    }
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "POP document has been uploaded successfully", response, null));
            }else{
                return ResponseEntity.ok(new ApiResponse<>(false, "POP document data is not valid, missing document key", null, null));
            }
        }catch (Exception exception){
            logger.error("POP document upload for Application id {} failed with exception: {}", applicationId, exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

}
