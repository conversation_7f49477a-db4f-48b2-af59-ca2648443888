package bw.org.hrdc.weblogic.workplacelearning.controller;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationConstants;
import bw.org.hrdc.weblogic.workplacelearning.dto.ResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanResponseDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.CourseDetails;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.service.IWorkPlaceTrainingPlanService;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.dto.ncbsc.ApplicationStatusUpdatePayload;
import bw.org.hrdc.weblogic.workplacelearning.dto.BatchStatusUpdateRequest;
import bw.org.hrdc.weblogic.workplacelearning.dto.BatchStatusUpdateResult;
import bw.org.hrdc.weblogic.workplacelearning.service.document.FileDocumentService;
import bw.org.hrdc.weblogic.workplacelearning.service.document.DocumentService;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.DocumentCommon;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanComments;
import bw.org.hrdc.weblogic.workplacelearning.service.WorkPlaceTrainingPlanCommentsService;
import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import org.hibernate.boot.registry.classloading.spi.ClassLoaderService.Work;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import javax.management.Notification;

import java.time.LocalDateTime;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * REST Controller for WorkPlace Training Plan.
 */
@Tag(
        name = "CRUD REST APIs for WorkPlace Training Plan",
        description = "CRUD REST APIs for managing WorkPlace Training Plans"
)
@RestController
@RequestMapping(path = "/api/v1/ncbsc/workplace-training-plan", produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
@Slf4j
public class WorkPlaceTrainingPlanController {

    private static final Logger logger = LoggerFactory.getLogger(WorkPlaceTrainingPlanController.class);

    private final IWorkPlaceTrainingPlanService trainingPlanService;
    private final WorkPlaceTrainingPlanCommentsService commentsService;
    private final WorkflowClient workflowClient;
    private final NotificationService notificationService;

    @Autowired
    private FileDocumentService fileDocumentService;
    
    @Autowired
    private DocumentService documentService;

    public WorkPlaceTrainingPlanController(IWorkPlaceTrainingPlanService trainingPlanService,
                                          WorkPlaceTrainingPlanCommentsService commentsService, WorkflowClient workflowClient, NotificationService notificationService) {
        this.notificationService = notificationService;
        this.trainingPlanService = trainingPlanService;
        this.commentsService = commentsService;
        this.workflowClient = workflowClient;
    }

    @Operation(
            summary = "Create, Save Draft, or Update Workplace Training Plan REST API",
            description = "REST API to create, save as draft, or update a Workplace Training Plan. " +
                         "If an ID is provided, it will update the plan. " +
                         "If no ID is provided and isDraft=true, it will create a draft plan. " +
                         "If no ID is provided and isDraft=false, it will create a submitted plan."
    )
    @PostMapping("/create")
    public ResponseEntity<WorkPlaceTrainingPlanResponseDto> createOrUpdateTrainingPlan(
            @Valid @RequestBody WorkPlaceTrainingPlanDto trainingPlanDto,
            @RequestParam(defaultValue = "false") boolean isDraft) {
        try {
            logger.info("Processing create/update request for workplace training plan, isDraft: {}", isDraft);
            WorkPlaceTrainingPlanResponseDto response = trainingPlanService.createOrUpdateTrainingPlan(trainingPlanDto, isDraft);

            try {
              Map<String, Object> workflowResponse = workflowClient.getNCBSCApplication(Enums.ApplicationType.WORK_SKILLS.name(), response.getApplicationId().toString());

                log.info("Workflow response: {}", workflowResponse);
                if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                    Object data = workflowResponse.get("applicationData");
                    if (data != null) {
                        String processInstanceId = (String) ((Map<String, Object>)workflowResponse).get("processInstanceId");
                        // Save the processInstanceId to the database
                        String preApprovalId = response.getApplicationId().toString();
                        trainingPlanService.updateProcessInstanceIdToApplication(preApprovalId, processInstanceId);
                        response.setProcessInstanceId(processInstanceId);
                        log.info("Process instance ID {} saved for application {}", processInstanceId, preApprovalId);
                    }
                }
            } catch (Exception e) {
                log.error("Failed to start workflow process: {}", e.getMessage());
                // Continue execution even if workflow process fails
            }

            WorkPlaceTrainingPlan application = trainingPlanService.fetchByReferenceNumber(response.getReferenceNumber());

            if(trainingPlanDto.getAttachments() != null && application != null){
                logger.info("Saved Application get Uuid {}", application.getId());
                trainingPlanDto.setAttachments(fileDocumentService.saveDocuments(String.valueOf(application.getId()), Enums.FileEntityType.TRAINING_PLAN_APPLICATION, trainingPlanDto.getAttachments()));
            }
            
            return ResponseEntity
                    .status(HttpStatus.CREATED)
                    .body(response);
        } catch (ResourceNotFoundException e) {
            logger.error("Resource not found: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error processing create/update request: {}", e.getMessage());
            throw e;
        }
    }


    @Operation(
            summary = "Fetch WorkPlace Training Plans REST API",
            description = "REST API to fetch WorkPlace Training Plans based on filters"
    )
    @GetMapping("/fetch")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTrainingPlans(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date submissionDate,
            @RequestParam(required = false) UUID userId,
            @RequestParam(required = false) UUID organisationId,
            @RequestParam(required = false) String applicationStatus,
            @RequestParam(required = false) String applicationState,
            @RequestParam(required = false) String programme,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String referenceNumber,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) String role,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "100") int size) {

        logger.info("Fetching training plans with criteria - role: {}, userId: {}, organisationId: {}, status: {}, programme: {}, referenceNumber: {}, search: {}",
                 role, userId, organisationId, applicationStatus, programme, referenceNumber, search);
        
        try {
            // For the frontend, we'll use a special format in the search parameter: "STATUS:STATE"
            if (search != null && search.contains(":")) {
                String[] parts = search.split(":", 2);
                if (parts.length == 2) {
                    // Convert to uppercase to match enum values
                    applicationStatus = parts[0].trim().toUpperCase();
                    applicationState = parts[1].trim().toUpperCase();
                    log.info("Extracted from search parameter: status={}, state={}", applicationStatus, applicationState);
                    
                    // Clear search to avoid double filtering
                    search = null;
                }
            }
            // Delegate to service layer
            Map<String, Object> response = trainingPlanService.getTrainingPlansByRole(
                    role, userId, organisationId, applicationStatus, applicationState, submissionDate, 
                    programme, startDate, endDate, search, referenceNumber, companyName,
                    pageNumber, size);
            
            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            logger.error("Failed to fetch training plans with exception: ", e);
            String errorMessage = String.format("Failed to fetch training plans: %s", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, errorMessage, null, null));
        }
    }

    @Operation(
            summary = "Get WorkPlace Training Plan by ID REST API",
            description = "REST API to get a WorkPlace Training Plan by ID with all related details including comments and documents"
    )
    @GetMapping("/application-id/{id}")
    public ResponseEntity<ApiResponse<?>> getApplicationById(@PathVariable UUID id) {
        try {
            WorkPlaceTrainingPlanDto application = trainingPlanService.getTrainingPlan(id);
            List<FileDocumentDto> attachments = fileDocumentService.getAttachmentsByEntityIdAndType(id, Enums.FileEntityType.TRAINING_PLAN_APPLICATION);

            return buildApplicationResponse(application, application.getId().toString(), attachments);
        } catch (ResourceNotFoundException e) {
            log.error("Application not found with application number {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        } catch (Exception e) {
            log.error("Failed to fetch application with application number {} with exception: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }
    
    @Operation(
            summary = "Get Pre-Approval Application by Reference Number REST API",
            description = "REST API to get a Pre-Approval Application by Reference Number with all related details including company, employees, training details, costs, comments, and documents"
    )
    @GetMapping("/reference-number/{referenceNumber}")
    public ResponseEntity<ApiResponse<?>> getApplicationByReferenceNumber(@PathVariable String referenceNumber) {
        try {
            WorkPlaceTrainingPlanDto application = trainingPlanService.getApplicationByReferenceNumber(referenceNumber);
            List<FileDocumentDto> attachments = fileDocumentService.getAttachmentsByEntityIdAndType(application.getId(), Enums.FileEntityType.TRAINING_PLAN_APPLICATION);

            return buildApplicationResponse(application, application.getId().toString(), attachments);
        } catch (ResourceNotFoundException e) {
            log.error("Application not found with reference number {}: {}", referenceNumber, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        } catch (Exception e) {
            log.error("Failed to fetch application with reference number {} with exception: {}", referenceNumber, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>(false, e.getMessage(), null, null));
        }
    }
    
    /**
     * Helper method to build a consistent response for application endpoints
     * 
     * @param application The application DTO
     * @param applicationId The application ID as a string
     * @return ResponseEntity with ApiResponse
     */
    private ResponseEntity<ApiResponse<?>> buildApplicationResponse(WorkPlaceTrainingPlanDto application, String applicationId, List<FileDocumentDto> attachments) {
            if(application != null) {
                Map<String, Object> response = new HashMap<>();

                // 1. Set main application data
                response.put("application", application);

                // 2. Get application comments/audit trail
                List<WorkPlaceTrainingPlanComments> comments = commentsService.getAuditLogsForTrainingPlan(UUID.fromString(applicationId));
                if(!comments.isEmpty()) {
                    List<Map<String, Object>> commentDtos = comments.stream()
                        .map(comment -> {
                            Map<String, Object> dto = new HashMap<>();
                            dto.put("id", comment.getId());
                            dto.put("trainingPlanId", comment.getTrainingPlan().getId());
                            dto.put("action", comment.getAction());
                            dto.put("comments", comment.getComments());
                            dto.put("updatedBy", comment.getUpdatedBy());
                            dto.put("timestamp", comment.getTimestamp());
                            return dto;
                        })
                        .collect(Collectors.toList());
                    response.put("comments", commentDtos);
                }

                // 3. Get application documents
                try {
                    List<DocumentCommon> documents = documentService.fetchDocumentsData(applicationId, Enums.FileEntityType.TRAINING_PLAN_APPLICATION.name());
                    response.put("documents", documents);
                    response.put("documentCount", documents.size());
                } catch (Exception e) {
                    log.error("Error fetching documents for training plan {}: {}", applicationId, e.getMessage());
                    response.put("documents", List.of());
                    response.put("documentCount", 0);
                }

                // 4. Add metadata
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("applicationId", applicationId);
                metadata.put("status", application.getApplicationStatus());
                metadata.put("state", application.getApplicationState());
                metadata.put("applicationNumber", application.getApplicationNumber());
                metadata.put("referenceNumber", application.getReferenceNumber());
                metadata.put("lastModified", application.getLastModifiedDate());
                response.put("metadata", metadata);

                if(attachments != null || !attachments.isEmpty()){
                    response.put("attachments", attachments);
                }else{
                    response.put("attachments", Collections.emptyList());
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", response, null));
            }
            
            // Return not found response if application is null
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponse<>(false, "Application not found", null, null));
    }


    @Operation(
            summary = "Soft Delete Workplace Training Plan REST API",
            description = "REST API to soft delete a Workplace Training Plan"
    )
    @DeleteMapping("/soft-delete/{id}")
    public ResponseEntity<Map<String, Object>> softDeleteTrainingPlan(@PathVariable UUID id) {
        try {
            trainingPlanService.softDeleteTrainingPlan(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("statusCode", ApplicationConstants.STATUS_200);
            response.put("statusMsg", "Workplace Training Plan soft deleted successfully.");
            
            return ResponseEntity.ok(response);
        } catch (ApplicationNotFoundException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errorCode", "RESOURCE_NOT_FOUND");
            errorResponse.put("errorMessage", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errorCode", "INTERNAL_SERVER_ERROR");
            errorResponse.put("errorMessage", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @Operation(
            summary = "Assign User to WorkPlace Training Plan REST API",
            description = "REST API to assign a user to a WorkPlace Training Plan"
    )
    @PutMapping("/{trainingPlanId}/assign-user")
    public ResponseEntity<ResponseDto> assignAgent(@PathVariable UUID trainingPlanId, @RequestBody Map<String, Object> payload) {
        log.info("Training plan user assignment initiated for training plan ID: {}", trainingPlanId);
        try {
            String role = payload.get("role").toString();
            String userId = payload.get("userId").toString();

            if(role.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                        "Required fields are missing, please provide user id and their respective role"));
            }

            Optional<WorkPlaceTrainingPlan> trainingPlan = trainingPlanService.getTrainingPlanEntity(trainingPlanId);

            if (trainingPlan.isPresent()) {
                int updatedResult = trainingPlanService.updateTrainingPlanAssignedUser(trainingPlanId, Enums.UserRoles.valueOf(role), userId);
                if (updatedResult == 0) {
                    logger.error("Failed to update training plan ID: {}", trainingPlanId);
                    return ResponseEntity.badRequest()
                        .body(new ResponseDto("TRAINING_PLAN_ERROR", "Failed to process training plan."));
                } else {
                     //TODO trigger notification to agent for the assignment made

                     if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    trainingPlan.get().getReferenceNumber(),
                                    trainingPlan.get().getId().toString(),
                                    trainingPlan.get().getApplicationStatus(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.WORK_SKILLS.name()
                                    
                            );
                    } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    trainingPlan.get().getReferenceNumber(),
                                    trainingPlan.get().getId().toString(),
                                    trainingPlan.get().getApplicationStatus(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.WORK_SKILLS.name()
                            );
                    } 
                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Training plan assigned successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ResponseDto("TRAINING_PLAN_NOT_FOUND",
                        "Provided training plan identifier does not exist"));
            }
        } catch (Exception e) {
            logger.error("Failed to assign training plan ID {} with exception: {}", trainingPlanId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }

    @Operation(
            summary = "Update Training Plan Status REST API",
            description = "REST API to update the status of a WorkPlace Training Plan"
    )
    @PutMapping("/{trainingPlanId}/status-update")
    public ResponseEntity<ResponseDto> changeTrainingPlanStatus(
            @PathVariable UUID trainingPlanId,
            @RequestBody ApplicationStatusUpdatePayload payload) {
                logger.info("Training plan status update initiated for training plan ID: {}", trainingPlanId);
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();

            String actionType = null;

            if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                actionType = "Agent_action";
            }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                actionType = "Officer_action";
            }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)) {
                actionType = "Manager_action";
            }

            String autoAssignRole = null;

            if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                autoAssignRole = Enums.UserRoles.OFFICER.name().toLowerCase();
            } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                autoAssignRole = Enums.UserRoles.MANAGER.name().toLowerCase();
            }

            if(action.isEmpty() || userId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING",
                        "Required fields are missing, please provide user id and action"));
            }

            Optional<WorkPlaceTrainingPlan> trainingPlan = trainingPlanService.getTrainingPlanEntity(trainingPlanId);
            if (trainingPlan.isPresent()) {
                int updatedResult = trainingPlanService.updateTrainingPlanStatus(trainingPlanId, role, action, newAssignee);
                if (updatedResult == 0) {
                    logger.error("Failed to update status of training plan ID: {}", trainingPlanId);
                    return ResponseEntity.badRequest()
                        .body(new ResponseDto("TRAINING_PLAN_ERROR", "Failed to process training plan."));
                } else {
                    // Auto-assign agent only after successful status update
                    try {
                        if (autoAssignRole != null && !autoAssignRole.trim().isEmpty()) {
                            trainingPlanService.updateAutoassignedAgent(trainingPlanId.toString(), autoAssignRole);
                        }
                    } catch (Exception e) {
                        logger.warn("Failed to auto-assign agent for training plan ID: {}, but status update was successful: {}", 
                                   trainingPlanId, e.getMessage());
                        // Continue execution even if auto-assignment fails
                    }
                    // Create and save comments if provided
                    if (comments != null && !comments.isEmpty()) {
                        WorkPlaceTrainingPlanComments logEntry = new WorkPlaceTrainingPlanComments();
                        logEntry.setTrainingPlan(trainingPlan.get());
                        logEntry.setAction(action);
                        logEntry.setComments(trainingPlanService.sanitizeHtml(comments));
                        logEntry.setUpdatedBy(userId);
                        logEntry.setTimestamp(LocalDateTime.now());
                        commentsService.createComments(logEntry);
                    }

                     String processInstanceId = trainingPlan.get().getProcessInstanceId();
                    String preApprovalId = trainingPlan.get().getId().toString();

                    if (processInstanceId != null && !processInstanceId.isEmpty()) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("referenceNumber", preApprovalId);
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.WORK_SKILLS.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, preApprovalId);
                            workflowClient.resumeProcess(processInstanceId, actionType, workflowPayload);
                        } catch (Exception e) {
                            // Log the error but continue with the application status update
                            logger.error("Failed to resume workflow process: {}", e.getMessage());
                            // Don't rethrow the exception - allow the application status update to succeed
                        }
                    } else {
                        logger.warn("No process instance ID found for application {}", preApprovalId);
                    }

                    return ResponseEntity.ok(new ResponseDto("SUCCESS", "Training plan status updated successfully"));
                }
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ResponseDto("TRAINING_PLAN_NOT_FOUND",
                        "Provided training plan identifier does not exist"));
            }
        } catch (Exception e) {
            logger.error("Failed to update status of training plan ID: {} with exception: {}", trainingPlanId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ResponseDto("INTERNAL_SERVER_ERROR", e.getMessage()));
        }
    }

    @Operation(
            summary = "Batch Update Training Plan Status REST API",
            description = "REST API to update the status of multiple WorkPlace Training Plans in a batch"
    )
    @PostMapping("/batch-status-update")
    public ResponseEntity<?> batchChangeTrainingPlanStatus(@RequestBody BatchStatusUpdateRequest payload) {
        logger.info("Batch training plan status update initiated for {} applications", 
                payload.getApplicationIds() != null ? payload.getApplicationIds().size() : 0);
        
        try {
            String role = payload.getRole();
            String userId = payload.getUserId();
            String action = payload.getAction();
            String comments = payload.getComments();
            String newAssignee = payload.getNewAssignee();
            List<UUID> applicationIds = payload.getApplicationIds();

            if (action.isEmpty() || userId.isEmpty() || applicationIds == null || applicationIds.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ResponseDto("MANDATORY_FIELDS_MISSING", 
                        "Required fields are missing, please provide user id, action and application IDs"));
            }

            BatchStatusUpdateResult result = trainingPlanService.batchUpdateTrainingPlanStatus(
                    applicationIds, role, action, userId, comments, newAssignee);
            
            return ResponseEntity.ok(new ApiResponse<>(true, 
                    "Batch status update processed: " + result.getSuccessCount() + " successful, " + 
                    result.getFailureCount() + " failed", result, null));
            
        } catch (Exception e) {
            logger.error("Failed to process batch status update with exception: {}", e.getMessage());
            return ApiResponse.getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{course_details_id}/course_details")
    public ResponseEntity<?> getCourseDetailsById(@PathVariable UUID course_details_id){
        try{

            if (course_details_id == null){
                return ResponseEntity.ok(new ApiResponse<>(false, "Required fields are missing, please provide course details id", null, null));
            }

            Optional<CourseDetails> courseDetails = trainingPlanService.getCourseDetailsEntity(course_details_id);

            if (courseDetails.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No record found", null, null));
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "Record found", courseDetails, null));
        }catch (Exception exception){
            logger.error("Error Fetching Course Details {} exception: {}", course_details_id, exception.getMessage());
            return getInternalServerError(exception.getMessage());
        }
    }

     @Operation(
            summary = "Update Paperless ID for WorkPlace Training Plan REST API",
            description = "REST API to update the paperless ID for a WorkPlace Training Plan and save document details to trn_documents table"
    )
    @PutMapping("/{applicationId}/email-pdf-attachments")
    public ResponseEntity<ApiResponse<?>> updatePaperlessIdToTrainingPlan(
            @PathVariable UUID applicationId,
            @RequestBody Map<String, String> payload) {
        
        log.info("Processing email PDF attachments for training plan ID: {} with payload: {}", applicationId, payload);
        
        try {
            String paperlessId = payload.get("paperlessId");
            
            if (paperlessId == null || paperlessId.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new ApiResponse<>(false, "PaperlessId is required", null, null));
            }
            
            // Use the service method that returns detailed response
            Map<String, Object> serviceResponse = trainingPlanService.updatePaperlessIdAndSaveDocument(
                applicationId.toString(), payload);
            
            // Check if the service operation was successful
            Boolean success = (Boolean) serviceResponse.get("success");
            
            if (Boolean.TRUE.equals(success)) {
                // Remove the success flag from response data as it's handled by ApiResponse
                Map<String, Object> responseData = new HashMap<>(serviceResponse);
                responseData.remove("success");
                
                String message = (String) serviceResponse.get("message");
                
                return ResponseEntity.ok(new ApiResponse<>(true, message, responseData, null));
                
            } else {
                String errorMessage = (String) serviceResponse.get("message");
                String errorType = (String) serviceResponse.get("errorType");
                
                if ("TRAINING_PLAN_NOT_FOUND".equals(errorType)) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, errorMessage, null, null));
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ApiResponse<>(false, errorMessage, null, null));
                }
            }
            
        } catch (Exception e) {
            log.error("Unexpected error processing email PDF attachments for training plan {}: {}", applicationId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(false, "Unexpected error occurred: " + e.getMessage(), null, null));
        }
    }
}
