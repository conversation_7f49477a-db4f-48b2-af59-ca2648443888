package com.workflowenginee.workflow.delegate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import com.workflowenginee.workflow.api.WorkplaceLearningClient;
import com.workflowenginee.workflow.model.Application;
import com.workflowenginee.workflow.util.ApiResponse;
import com.workflowenginee.workflow.util.Enums;

@Component("fetchDataDelegate")
public class FetchDataDelegate implements JavaDelegate {

    private final WorkplaceLearningClient workplaceLearningClient;

    @Autowired
    public FetchDataDelegate(WorkplaceLearningClient workplaceLearningClient) {
        this.workplaceLearningClient = workplaceLearningClient;
    }

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String applicationNumber = (String) execution.getVariable("applicationNumber");
        String applicationType = (String) execution.getVariable("applicationType");
        String role = (String) execution.getVariable("role");

        System.out.println("Application Number: " + applicationNumber);
        System.out.println("[Process: " + processInstanceId + "] Starting fetch for application: " + applicationNumber);

        ApiResponse<?> response = null;

        try {
            if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType)) {
                try {
                    response = workplaceLearningClient.getApplicationByReferenceNumber(applicationNumber);

                    if (response != null && response.getData() instanceof Map) {
                        try {
                            Map<String, Object> responseData = (Map<String, Object>) response.getData();
                            execution.setVariable("ApplicationData", responseData);
                            execution.setVariable("ApplicationType", applicationType);
                            if (responseData.containsKey("application")) {
                                Map<String, Object> application = (Map<String, Object>) responseData.get("application");
                                execution.setVariable("applicationStatus", application.get("applicationStatus"));
                                execution.setVariable("applicationState", application.get("applicationState") );
                                execution.setVariable("role", role);
                                execution.setVariable("applicationType", applicationType);
                                String assignedAgent = (String) application.get("assignedAgent");
                            String assignedOfficer = (String) application.get("assignedOfficer");
                            String assignedManager = (String) application.get("assignedManager");
                            String notifyToassignedBackOfficer =null;

                            System.out.println("assignedAgent :: "+ assignedAgent);
                            System.out.println("assignedOfficer :: "+ assignedOfficer);
                            System.out.println("assignedManager :: "+ assignedManager);


                            if (assignedAgent !=null && assignedOfficer == null && assignedManager == null) {
                                System.out.println("111111111111111111!!");
                                notifyToassignedBackOfficer = assignedAgent;
                            } else if (assignedAgent != null && assignedOfficer !=null && assignedManager == null) {
                                System.out.println("222222222222222222!!");
                                notifyToassignedBackOfficer = assignedOfficer;
                            } else if (assignedAgent != null && assignedOfficer != null && assignedManager != null) {
                                System.out.println("333333333333333333!!");
                                notifyToassignedBackOfficer = assignedManager;
                            } else {
                                notifyToassignedBackOfficer = null;  
                            }
                            System.out.println("notifyToassignedBackOfficer :: " + notifyToassignedBackOfficer);
                            execution.setVariable("notifyToassignedBackOfficer", notifyToassignedBackOfficer);
                                if (application.containsKey("organisationId")) {
                                    String organisationId = String.valueOf(application.get("organisationId"));
                                    execution.setVariable("companyId", organisationId);
                                    System.out.println(
                                            "[Process: " + processInstanceId + "] organisationId: " + organisationId);
                                } else {
                                    System.err.println("[Process: " + processInstanceId
                                            + "] organisationId not found in application data");
                                }

                                System.out.println("[Process: " + processInstanceId + "] Application state: "
                                        + application.get("applicationState"));
                            }

                            System.out.println(
                                    "[Process: " + processInstanceId + "] Application data processed successfully");

                        } catch (ClassCastException castEx) {
                            System.err.println("[Process: " + processInstanceId + "] Error parsing application data:");
                            castEx.printStackTrace();
                            execution.setVariable("fetchError", "Invalid application data format");
                        }

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] No valid response data received");
                        execution.setVariable("fetchError", "No data returned from application service");
                    }

                } catch (Exception serviceEx) {
                    System.err.println(
                            "[Process: " + processInstanceId + "] Error connecting to WORKPLACE-LEARNING service:");
                    serviceEx.printStackTrace();

                    // Fallback mock data
                    Map<String, Object> mockApplication = new HashMap<>();
                    mockApplication.put("Id", "MOCK-" + UUID.randomUUID());
                    mockApplication.put("referenceNumber", applicationNumber);
                    mockApplication.put("applicationStatus", "PENDING");

                    Map<String, Object> mockData = new HashMap<>();
                    mockData.put("application", mockApplication);

                    response = new ApiResponse<>(true, "Mock data used due to service failure", null, null);
                }

            } else if (Enums.ApplicationType.PRE_APPROVAL.name().equalsIgnoreCase(applicationType)) {

                try {
                    response = workplaceLearningClient.getApplicationById(applicationNumber);

                    if (response != null && response.getData() instanceof Map) {
                        try {
                            Map<String, Object> responseData = (Map<String, Object>) response.getData();
                            execution.setVariable("ApplicationData", responseData);
                            execution.setVariable("ApplicationType", applicationType);

                            if (responseData.containsKey("application")) {
                                Map<String, Object> application = (Map<String, Object>) responseData.get("application");
                                execution.setVariable("applicationStatus", application.get("status"));
                                execution.setVariable("role", role);
                                execution.setVariable("applicationType", applicationType);
                                execution.setVariable("applicationState", application.get("state"));
                                if (application.containsKey("organisationId")) {
                                    String organisationId = String.valueOf(application.get("organisationId"));
                                    execution.setVariable("companyId", organisationId);
                                    System.out.println(
                                            "[Process: " + processInstanceId + "] organisationId: " + organisationId);
                                } else {
                                    System.err.println("[Process: " + processInstanceId
                                            + "] organisationId not found in application data");
                                }

                                System.out.println("[Process: " + processInstanceId + "] Application status: "
                                        + application.get("applicationStatus"));
                            }

                            System.out.println(
                                    "[Process: " + processInstanceId + "] Application data processed successfully");

                        } catch (ClassCastException castEx) {
                            System.err.println("[Process: " + processInstanceId + "] Error parsing application data:");
                            castEx.printStackTrace();
                            execution.setVariable("fetchError", "Invalid application data format");
                        }

                    } else {
                        System.err.println("[Process: " + processInstanceId + "] No valid response data received");
                        execution.setVariable("fetchError", "No data returned from application service");
                    }
                } catch (Exception serviceEx) {
                    System.err.println(
                            "[Process: " + processInstanceId + "] Error connecting to WORKPLACE-LEARNING service:");
                    serviceEx.printStackTrace();

                    // Fallback mock data
                    Map<String, Object> mockApplication = new HashMap<>();
                    mockApplication.put("Id", "MOCK-" + UUID.randomUUID());
                    mockApplication.put("referenceNumber", applicationNumber);
                    mockApplication.put("applicationStatus", "PENDING");

                    Map<String, Object> mockData = new HashMap<>();
                    mockData.put("application", mockApplication);

                    response = new ApiResponse<>(true, "Mock data used due to service failure", null, null);
                }

            } else if (Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType)) {
                // Uncomment and implement if needed
                response = workplaceLearningClient.getApplicationById(UUID.fromString(applicationNumber));

                if (response != null && response.getData() instanceof Map) {
                    try {
                        Map<String, Object> responseData = (Map<String, Object>) response.getData();
                        execution.setVariable("ApplicationData", responseData);
                        execution.setVariable("ApplicationType", applicationType);

                        if (responseData.containsKey("application")) {
                            Map<String, Object> application = (Map<String, Object>) responseData.get("application");
                            execution.setVariable("applicationStatus", application.get("applicationStatus"));
                            execution.setVariable("role", role);
                            execution.setVariable("applicationType", applicationType);
                            execution.setVariable("applicationState", application.get("applicationState"));
                            // execution.setVariable("assignedAgent", application.get("assignedAgent"));
                            // execution.setVariable("assignedOfficer", application.get("assignedOfficer"));
                            // execution.setVariable("assignedManager", application.get("assignedManager"));
                            String assignedAgent = (String) application.get("assignedAgent");
                            String assignedOfficer = (String) application.get("assignedOfficer");
                            String assignedManager = (String) application.get("assignedManager");
                            String notifyToassignedBackOfficer =null;

                            System.out.println("assignedAgent :: "+ assignedAgent);
                            System.out.println("assignedOfficer :: "+ assignedOfficer);
                            System.out.println("assignedManager :: "+ assignedManager);


                            if (assignedAgent !=null && assignedOfficer == null && assignedManager == null) {
                                System.out.println("111111111111111111!!");
                                notifyToassignedBackOfficer = assignedAgent;
                            } else if (assignedAgent != null && assignedOfficer !=null && assignedManager == null) {
                                System.out.println("222222222222222222!!");
                                notifyToassignedBackOfficer = assignedOfficer;
                            } else if (assignedAgent != null && assignedOfficer != null && assignedManager != null) {
                                System.out.println("333333333333333333!!");
                                notifyToassignedBackOfficer = assignedManager;
                            } else {
                                notifyToassignedBackOfficer = null;  
                            }
                            System.out.println("notifyToassignedBackOfficer :: " + notifyToassignedBackOfficer);
                            execution.setVariable("notifyToassignedBackOfficer", notifyToassignedBackOfficer);

                            if (application.containsKey("organisationId")) {
                                String organisationId = String.valueOf(application.get("organisationId"));
                                execution.setVariable("companyId", organisationId);
                                System.out.println(
                                "[Process: " + processInstanceId + "] organisationId: " + organisationId);
                            } else {
                                System.err.println("[Process: " + processInstanceId
                                        + "] organisationId not found in application data");
                            }

                            System.out.println("[Process: " + processInstanceId + "] Application status: "
                                    + application.get("applicationStatus"));
                        }

                        System.out.println(
                                "[Process: " + processInstanceId + "] Application data processed successfully");

                    } catch (ClassCastException castEx) {
                        System.err.println("[Process: " + processInstanceId + "] Error parsing application data:");
                        castEx.printStackTrace();
                        execution.setVariable("fetchError", "Invalid application data format");
                    }

                } else {
                    System.err.println("[Process: " + processInstanceId + "] No valid response data received");
                    execution.setVariable("fetchError", "No data returned from application service");
                }

            } else if (Enums.ApplicationType.NOC.name().equalsIgnoreCase(applicationType)) {

                response = workplaceLearningClient.searchByReferenceNumber(applicationNumber);

                if (response != null && response.getData() instanceof Map) {
                    try {

                        Map<String, Object> responseData = (Map<String, Object>) response.getData();
                        execution.setVariable("ApplicationData", responseData);
                        execution.setVariable("ApplicationType", applicationType);

                        if (responseData.containsKey("application")) {
                            Map<String, Object> application = (Map<String, Object>) responseData.get("application");
                            execution.setVariable("applicationState", application.get("applicationState"));
                            execution.setVariable("applicationStatus", application.get("applicationStatus"));
                            execution.setVariable("role", role);
                            execution.setVariable("applicationType", applicationType);
                            if (application.containsKey("organisationId")) {
                                String organisationId = String.valueOf(application.get("organisationId"));
                                execution.setVariable("companyId", organisationId);
                                System.out.println(
                                        "[Process: " + processInstanceId + "] organisationId: " + organisationId);
                            } else {
                                System.err.println("[Process: " + processInstanceId
                                        + "] organisationId not found in application data");
                            }

                            System.out.println("[Process: " + processInstanceId + "] Application status: "
                                    + application.get("applicationStatus"));
                        }

                        System.out.println(
                                "[Process: " + processInstanceId + "] Application data processed successfully");

                    } catch (ClassCastException castEx) {
                        System.err.println("[Process: " + processInstanceId + "] Error parsing application data:");
                        castEx.printStackTrace();
                        execution.setVariable("fetchError", "Invalid application data format");
                    }

                } else {
                    System.err.println("[Process: " + processInstanceId + "] No valid response data received");
                    execution.setVariable("fetchError", "No data returned from application service");
                }

            }

        } catch (Exception ex) {
            System.err.println("[Process: " + processInstanceId + "] Unexpected error occurred:");
            ex.printStackTrace();
            execution.setVariable("fetchError", "General error during application fetch");
        }
    }

}
