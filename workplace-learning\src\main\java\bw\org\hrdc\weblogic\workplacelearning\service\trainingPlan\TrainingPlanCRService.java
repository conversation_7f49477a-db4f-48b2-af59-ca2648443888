package bw.org.hrdc.weblogic.workplacelearning.service.trainingPlan;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.workskillsTraining.WorkPlaceTrainingPlanChangeRequestDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlan;
import bw.org.hrdc.weblogic.workplacelearning.entity.workskillsTraining.WorkPlaceTrainingPlanChangeRequest;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.mapper.workplaceTraining.TrainingPlanChangeRequestMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.TrainingPlanCRRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.Filters;
import bw.org.hrdc.weblogic.workplacelearning.util.RecordDataResponseHelper;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:34
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:34
 */

@Service
public class TrainingPlanCRService {

    @Autowired
    private WorkPlaceTrainingPlanRepository trainingPlanRepository;

    @Autowired
    private TrainingPlanCRRepository changeRequestRepo;

    @Autowired
    private CompanyClient companyClient;

    private static final Logger log = LoggerFactory.getLogger(TrainingPlanCRService.class);

    @Transactional
    public WorkPlaceTrainingPlanChangeRequest create(WorkPlaceTrainingPlanChangeRequestDto app) {
        try {
            log.info("Processing create request for workplace training plan CR");

            if(isDuplicateChangeRequest(app.getOrganisationId())){
                throw new Exception("Duplicate application, there is already an existing application in progress");
            }

            if (app.getTrainingPlanId() == null) {
                throw new IllegalArgumentException("Training Plan ID must not be null");
            }

            Optional<WorkPlaceTrainingPlan> trainingPlan = trainingPlanRepository.findById(UUID.fromString(app.getTrainingPlanId()));
            if(trainingPlan.isPresent()){
                WorkPlaceTrainingPlanChangeRequest application = TrainingPlanChangeRequestMapper.toEntity(app);
                application.setTrainingPlan(trainingPlan.get());
                WorkPlaceTrainingPlanChangeRequest savedPlan = changeRequestRepo.save(application);
                log.info("WorkPlaceTrainingPlan created with ID: {}", savedPlan.getId());
                return savedPlan;
            }else{
                return null;
            }
        }  catch (IllegalArgumentException e) {
            log.error("Error processing create request: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Error processing create request: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create workplace training plan CR ", e);
        }
    }

    @Transactional
    public Optional<WorkPlaceTrainingPlanChangeRequest> update(UUID id, WorkPlaceTrainingPlanChangeRequest app) {
        try {
            log.info("Processing update request for workplace training plan CR");
            return changeRequestRepo.findById(id).map(existing -> {
                app.setId(existing.getId());
                app.setUuid(existing.getUuid());

                existing.setFinancialYear(app.getFinancialYear());
                existing.setLocation(app.getLocation());
                existing.setContactDate(app.getContactDate());
                existing.setSubmissionDate(app.getSubmissionDate());
                existing.setCourseDetails(app.getCourseDetails());
                existing.setProcessInstanceId(app.getProcessInstanceId());
                existing.setApplicationStatus(app.getApplicationStatus());
                existing.setApplicationState(app.getApplicationState());
                existing.setReferenceNumber(app.getReferenceNumber());

                return changeRequestRepo.save(existing);
            });
        } catch (Exception e) {
            log.error("Error processing update request: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    public Map<String, Object> getTrainingPlansByRole(
            String role, UUID userId, UUID organisationId, String applicationStatus, String applicationState,
            Date submissionDate, Date startDate, Date endDate,
            String search, String referenceNumber, String companyName, int pageNumber, int size) {

        log.info("Service: Fetching training plans with criteria - role: {}, userId: {}, organisationId: {}, status: {}, state: {}, referenceNumber: {}, search: {}",
                role, userId, organisationId, applicationStatus, applicationState, referenceNumber, search);
        Filters<WorkPlaceTrainingPlanChangeRequest> filters = new Filters<>();
        Specification<WorkPlaceTrainingPlanChangeRequest> baseRoleSpec = filters.buildBaseRoleSpecification(role, userId, organisationId);

        Specification<WorkPlaceTrainingPlanChangeRequest> fullSpec = (root, query, cb) -> {
            Predicate basePredicate = baseRoleSpec.toPredicate(root, query, cb);

            List<Predicate> predicates = new ArrayList<>(filters.buildDateRangePredicates(cb, root, startDate, endDate));
            if(applicationStatus != null && !applicationStatus.isEmpty()) {
                predicates.addAll(filters.buildApplicationStatusPredicate(cb, root, applicationStatus));
            }
            if(applicationState != null && !applicationState.isEmpty()) {
                predicates.addAll(filters.buildApplicationStatePredicate(cb, root, applicationState));
            }
            if(submissionDate != null) {
                predicates.addAll(filters.buildSubmissionDatePredicate(cb, root, submissionDate));
            }
            if(referenceNumber != null && !referenceNumber.isEmpty()) {
                predicates.addAll(filters.referenceNumberPredicate(cb, root, referenceNumber));
            }
            return cb.and(Stream.concat(Stream.of(basePredicate), predicates.stream()).toArray(Predicate[]::new));
        };

        // Execute query with pagination
        Pageable pageable = PageRequest.of(pageNumber, size, Sort.by("createdAt").descending());
        log.info("Pageable pageable: ", pageable);
        Page<WorkPlaceTrainingPlanChangeRequest> trainingPlans = changeRequestRepo.findAll(fullSpec, pageable);

        RecordDataResponseHelper<WorkPlaceTrainingPlanChangeRequest> dataResponseHelper = new RecordDataResponseHelper<>();
        if (trainingPlans.isEmpty()) {
            return dataResponseHelper.createEmptyResponse(pageNumber, size);
        }

        Map<String, Object> statusCounts = getTrainingPlanStatusSummary();

        List<Map<String, Object>> trainingPlanDtos = trainingPlans.getContent().stream()
                .map(plan->{
                    Map<String, Object> planMap = new LinkedHashMap<>();

                    planMap.put("id", plan.getUuid());
                    planMap.put("organisationId", plan.getOrganisationId());
                    planMap.put("applicationState", plan.getApplicationState());
                    planMap.put("applicationStatus", plan.getApplicationStatus());
                    planMap.put("referenceNumber", plan.getReferenceNumber());
                    planMap.put("applicationNumber", plan.getApplicationNumber());

                    planMap.put("companyName", null);

                    planMap.put("submissionDate", plan.getSubmissionDate());

                    // Set assignedTo based on state and status - ensure it's always included
                    if (plan.getApplicationState() != null) {
                        if ("IN_APPROVAL".equals(plan.getApplicationState()) &&
                                !"CHANGE_REQUEST".equals(plan.getApplicationStatus())) {
                            planMap.put("assignedTo", plan.getAssignedManager());
                        } else if ("IN_REVIEW".equals(plan.getApplicationState()) &&
                                !"CHANGE_REQUEST".equals(plan.getApplicationStatus())) {
                            planMap.put("assignedTo", plan.getAssignedOfficer());
                        } else if ("IN_PROCESSING".equals(plan.getApplicationState()) &&
                                !"CHANGE_REQUEST".equals(plan.getApplicationStatus())) {
                            planMap.put("assignedTo", plan.getAssignedAgent());
                        } else if ("SUBMITTED".equals(plan.getApplicationState()) &&
                                !"CHANGE_REQUEST".equals(plan.getApplicationStatus())) {
                            planMap.put("assignedTo", plan.getAssignedAgentLead());
                        } else {
                            planMap.put("assignedTo", null);
                        }
                    } else {
                        planMap.put("assignedTo", null);
                    }

                    try {
                        if (plan.getOrganisationId() != null) {
                            ApiResponse<?> companyResponse = companyClient.fetchCompanyById(plan.getOrganisationId());
                            if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                                Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                                planMap.put("companyName", companyData.get("name"));
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error fetching company details for training plan {}: {}", plan.getId(), e.getMessage());
                    }
                    return planMap;
                })
                .collect(Collectors.toList());

        // Build response
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> metadata = dataResponseHelper.createMetadata( trainingPlans, statusCounts);
        response.put("metadata", metadata);
        response.put("content", trainingPlanDtos);

        return response;
    }

    public WorkPlaceTrainingPlanChangeRequest getTrainingPlan(UUID id) {
        log.info("Fetching workplace training plan by ID: {}", id);

        Optional<WorkPlaceTrainingPlanChangeRequest> request = changeRequestRepo.findByUuId(id.toString());

        if (request.isEmpty()){
            throw new ResourceNotFoundException("WorkPlace Training Plan not found with ID: " + id);
        }

        return request.get();
    }

    @Transactional
    public void softDeleteTrainingPlan(UUID id) {
        log.info("Soft deleting workplace training plan with ID: {}", id);

        Optional<WorkPlaceTrainingPlanChangeRequest> trainingPlan = changeRequestRepo.findByUuId(id.toString());

        if(trainingPlan.isEmpty()) {
            throw(new ApplicationNotFoundException("Training Plan not found with ID: " + id));
        }

        WorkPlaceTrainingPlanChangeRequest trainingPlanObject = trainingPlan.get();

        // Set deleted flag instead of actually deleting
        trainingPlanObject.setDeleted(true);
        trainingPlanObject.setUpdatedAt(LocalDateTime.now());

        changeRequestRepo.save(trainingPlanObject);
        log.info("Workplace training plan soft deleted with ID: {}", id);
    }
    @Transactional
    public int updateTrainingPlanAssignedUser(UUID id, Enums.UserRoles role, String userId) {
        return changeRequestRepo.updateTrainingPlanAssignedUser(String.valueOf(id), role.name(), userId);
    }

    @Transactional
    public int updateTrainingPlanStatus(UUID trainingPlanId, String role, String action, String newAssignee) {
        log.info("Updating status for training plan ID: {}, role: {}, action: {}, newAssignee: {}",
                trainingPlanId, role, action, newAssignee);

        return changeRequestRepo.changeTrainingPlanStatus(String.valueOf(trainingPlanId), role, action, newAssignee);
    }

    public Map<String, Object> getTrainingPlanStatusSummary() {
        Specification<WorkPlaceTrainingPlanChangeRequest> baseSpec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), false));
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        List<WorkPlaceTrainingPlanChangeRequest> requests = changeRequestRepo.findAll(baseSpec);

        Map<String, Long> counts = new HashMap<>();
        counts.put("underReview", requests.stream().filter(r -> Enums.Status.PRE_APPROVED.name().equals(r.getApplicationStatus())).count());
        counts.put("approved", requests.stream().filter(r -> Enums.Status.APPROVED.name().equals(r.getApplicationStatus())).count());
        counts.put("pendingVetting", requests.stream().filter(r -> Enums.Status.PENDING.name().equals(r.getApplicationStatus())).count());
        counts.put("awaitingChanges", requests.stream().filter(r -> Enums.Status.CHANGE_REQUEST.name().equals(r.getApplicationStatus())).count());
        counts.put("vettingOngoing", requests.stream().filter(r -> Enums.Status.PENDING_PAYMENT.name().equals(r.getApplicationStatus())).count());
        counts.put("rejected", requests.stream().filter(r -> Enums.Status.REJECTED.name().equals(r.getApplicationStatus())).count());
        counts.put("pendingApproval", requests.stream().filter(r -> Enums.Status.PENDING.name().equals(r.getApplicationStatus())).count());
        counts.put("totalApplications", (long) requests.size());

        return new HashMap<>(counts);
    }

    private boolean isDuplicateChangeRequest(String organisationId) {
        return changeRequestRepo.existsByOrganisationIdAndApplicationStatusNotIn(
                organisationId,
                List.of("APPROVED", "REJECTED")
        );
    }
}
