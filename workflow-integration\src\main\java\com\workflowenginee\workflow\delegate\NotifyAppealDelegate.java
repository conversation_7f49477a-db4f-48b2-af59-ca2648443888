package com.workflowenginee.workflow.delegate;

import java.util.Map;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.workflowenginee.workflow.dto.NotifySpecificUserDto;
import com.workflowenginee.workflow.dto.NotifyToClientDto;
import com.workflowenginee.workflow.service.NotificationComplaintService;
import com.workflowenginee.workflow.util.Enums;

import lombok.extern.slf4j.Slf4j;

@Component("notifyAppealDelegate")
@Slf4j
public class NotifyAppealDelegate implements JavaDelegate {

    @Autowired
    private NotificationComplaintService notificationService;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        String appealId = (String) execution.getVariable("appealId");
        String currentActivityId = execution.getCurrentActivityId();

        log.info("[Process: {}] Executing notification for appeal: {} at activity: {}", 
            processInstanceId, appealId, currentActivityId);

        try {
            Map<String, Object> appealData = (Map<String, Object>) execution.getVariable("appealData");

            if (appealData != null) {
                String assignedTo = (String) appealData.get("assignedTo");
                String referenceNumber = (String) appealData.get("referenceNumber");
                String organisationId = (String) appealData.get("organisationId");
                String status = (String) appealData.get("status");
                String state = (String) appealData.get("state");
                String createdBy = (String) appealData.get("createdBy");
                String rejectedByUserId = (String) execution.getVariable("rejectedByUserId");

                // Default messages that can be overridden by process variables
                String clientMessage = (String) execution.getVariable("clientMessage");
                String managerMessage = (String) execution.getVariable("managerMessage");
                String rejectedByMessage = (String) execution.getVariable("rejectedByMessage");
                
                // Determine notification type based on current activity
                String notificationType = determineNotificationType(currentActivityId);
                
                log.info("[Process: {}] Notification type: {} for appeal: {}", 
                    processInstanceId, notificationType, referenceNumber);

                // Always notify client
                notifyClient(execution, appealData, clientMessage);
                
                // Determine if we need to notify manager and rejected user based on activity
                if ("INITIAL_NOTIFICATION".equals(notificationType)) {
                    // For initial notification, notify the assigned manager and rejected user
                    if (assignedTo != null) {
                        notifyManager(execution, appealData, assignedTo, managerMessage);
                        log.info("[Process: {}] Manager notification sent to: {}", processInstanceId, assignedTo);
                    }
                    
                    if (rejectedByUserId != null) {
                        notifyRejectedUser(execution, appealData, rejectedByUserId, rejectedByMessage);
                        log.info("[Process: {}] Rejected user notification sent to: {}", processInstanceId, rejectedByUserId);
                    }
                } else if ("CLOSURE_NOTIFICATION".equals(notificationType) || "REJECTION_NOTIFICATION".equals(notificationType)) {
                    // For closure and rejection notifications, also notify the rejected user
                    if (rejectedByUserId != null) {
                        notifyRejectedUser(execution, appealData, rejectedByUserId, rejectedByMessage);
                        log.info("[Process: {}] Rejected user notification sent for {} to: {}", 
                            processInstanceId, notificationType, rejectedByUserId);
                    }
                }
                
                execution.setVariable("notificationSent", true);
                execution.setVariable("notificationTimestamp", System.currentTimeMillis());
            } else { 
                log.warn("[Process: {}] No appeal data available for notification", processInstanceId);
                execution.setVariable("notificationSent", false);
                execution.setVariable("notificationError", "No appeal data available");
            }
        } catch (Exception e) {
            log.error("[Process: {}] Error sending notifications: {}", processInstanceId, e.getMessage(), e);
            execution.setVariable("notificationSent", false);
            execution.setVariable("notificationError", e.getMessage());
        }
    }
    
    /**
     * Determines the type of notification based on the current activity ID
     */
    private String determineNotificationType(String activityId) {
        if (activityId == null) {
            return "UNKNOWN";
        }
        
        switch (activityId) {
            case "serviceTask_NotifyAllParties":
                return "INITIAL_NOTIFICATION";
            case "serviceTask_NotifyClosure":
                return "CLOSURE_NOTIFICATION";
            case "serviceTask_NotifyRejection":
                return "REJECTION_NOTIFICATION";
            default:
                return "GENERAL_NOTIFICATION";
        }
    }
    
    /**
     * Notifies the client about the appeal status
     */
    private void notifyClient(DelegateExecution execution, Map<String, Object> appealData, String customMessage) {
        String processInstanceId = execution.getProcessInstanceId();
        String appealId = (String) execution.getVariable("appealId");
        String referenceNumber = (String) appealData.get("referenceNumber");
        String organisationId = (String) appealData.get("organisationId");
        String status = (String) appealData.get("status");
        String state = (String) appealData.get("state");
        
        // Determine appropriate message based on state if custom message not provided
        String message;
        if (customMessage != null) {
            message = customMessage;
        } else {
            message = getDefaultClientMessage(state, referenceNumber);
        }
        
        NotifyToClientDto notifyClientDto = NotifyToClientDto.builder()
            .referenceNumber(referenceNumber)
            .applicationId(appealId)
            .applicationStatus(status)
            .companyId(organisationId)
            .applicationState(state)
            .applicationType(Enums.ApplicationType.APPEALS.name())
            .message(message)
            .build();
            
        notificationService.notificationToClient(notifyClientDto);
        log.info("[Process: {}] Client notification sent for appeal: {}", processInstanceId, referenceNumber);
    }
    
    /**
     * Notifies the assigned manager about the appeal
     */
    private void notifyManager(DelegateExecution execution, Map<String, Object> appealData, 
                            String managerId, String customMessage) {
        String processInstanceId = execution.getProcessInstanceId();
        String appealId = (String) execution.getVariable("appealId");
        String referenceNumber = (String) appealData.get("referenceNumber");
        String state = (String) appealData.get("state");
        
        // Determine appropriate message based on state if custom message not provided
        String message;
        if (customMessage != null) {
            message = customMessage;
        } else {
            message = getDefaultManagerMessage(state, referenceNumber);
        }
        
        String subject = getManagerNotificationSubject(state);
        
        NotifySpecificUserDto notificationDto = NotifySpecificUserDto.builder()
            .userId(managerId)
            .referenceNumber(referenceNumber != null ? referenceNumber : appealId)
            .applicationId(appealId)
            .subject(subject)
            .message(message)
            .applicationType(Enums.ApplicationType.APPEALS.name())
            .build();
            
        notificationService.notifySpecificUser(notificationDto);
        execution.setVariable("notifiedManager", managerId);
    }
    
    /**
     * Notifies the user who rejected the original application
     */
    private void notifyRejectedUser(DelegateExecution execution, Map<String, Object> appealData, 
                                  String rejectedUserId, String customMessage) {
        String processInstanceId = execution.getProcessInstanceId();
        String appealId = (String) execution.getVariable("appealId");
        String referenceNumber = (String) appealData.get("referenceNumber");
        String originalApplicationReference = (String) execution.getVariable("originalApplicationReference");
        String currentActivityId = execution.getCurrentActivityId();
        String state = (String) appealData.get("state");
        
        // Determine appropriate message if custom message not provided
        String message;
        String subject;
        if (customMessage != null) {
            message = customMessage;
            subject = getDefaultRejectedUserSubject(currentActivityId, state);
        } else {
            message = getDefaultRejectedUserMessage(currentActivityId, state, originalApplicationReference, referenceNumber);
            subject = getDefaultRejectedUserSubject(currentActivityId, state);
        }
        
        NotifySpecificUserDto notificationDto = NotifySpecificUserDto.builder()
            .userId(rejectedUserId)
            .referenceNumber(referenceNumber != null ? referenceNumber : appealId)
            .applicationId(appealId)
            .subject(subject)
            .message(message)
            .applicationType(Enums.ApplicationType.APPEALS.name())
            .build();
            
        notificationService.notifySpecificUser(notificationDto);
        execution.setVariable("notifiedRejectedUser", rejectedUserId);
    }
    
    /**
     * Gets the default message for client notifications based on appeal state
     */
    private String getDefaultClientMessage(String state, String referenceNumber) {
        if (state == null) {
            return "Your appeal " + referenceNumber + " has been updated";
        }
        
        switch (state) {
            case "SUBMITTED":
                return "Your appeal " + referenceNumber + " has been created and assigned to a manager for review";
            case "UNDER_REVIEW":
                return "Your appeal " + referenceNumber + " is currently under review by our team";
            case "COMPLETED":
                return "Your appeal " + referenceNumber + " has been closed and your application is resubmitted";
            case "REJECTED":
                return "Your appeal " + referenceNumber + " has been rejected";
            default:
                return "Your appeal " + referenceNumber + " status has been updated to " + state;
        }
    }
    
    /**
     * Gets the default message for manager notifications based on appeal state
     */
    private String getDefaultManagerMessage(String state, String referenceNumber) {
        if (state == null) {
            return "Appeal " + referenceNumber + " has been assigned to you for review";
        }
        
        switch (state) {
            case "SUBMITTED":
                return "New appeal " + referenceNumber + " has been created and assigned to you for review";
            case "UNDER_REVIEW":
                return "Appeal " + referenceNumber + " requires your attention for review";
            default:
                return "Appeal " + referenceNumber + " with state " + state + " has been assigned to you";
        }
    }
    
    /**
     * Gets the appropriate notification subject for manager notifications based on appeal state
     */
    private String getManagerNotificationSubject(String state) {
        if (state == null) {
            return "Appeal Assignment";
        }
        
        switch (state) {
            case "SUBMITTED":
                return "New Appeal Assignment";
            case "UNDER_REVIEW":
                return "Appeal Review Required";
            case "COMPLETED":
                return "Appeal Resolution Notification";
            case "REJECTED":
                return "Appeal Rejection Notification";
            default:
                return "Appeal Status Update: " + state;
        }
    }
    
    /**
     * Gets the default message for rejected user notifications based on activity and state
     */
    private String getDefaultRejectedUserMessage(String activityId, String state, 
                                               String originalApplicationReference, String referenceNumber) {
        if (activityId == null) {
            return "The application you rejected (Reference: " + originalApplicationReference + 
                   ") has been appealed. Appeal reference: " + referenceNumber;
        }
        
        switch (activityId) {
            case "serviceTask_NotifyAllParties":
                return "The application you rejected (Reference: " + originalApplicationReference + 
                       ") has been appealed. Appeal reference: " + referenceNumber;
            case "serviceTask_NotifyClosure":
                return "The appeal (Reference: " + referenceNumber + ") for the application you rejected (Reference: " + 
                       originalApplicationReference + ") has been closed and the application is being resubmitted.";
            case "serviceTask_NotifyRejection":
                return "The appeal (Reference: " + referenceNumber + ") for the application you rejected (Reference: " + 
                       originalApplicationReference + ") has been rejected by the manager.";
            default:
                return "Update on the appeal (Reference: " + referenceNumber + ") for the application you rejected (Reference: " + 
                       originalApplicationReference + "). Status: " + state;
        }
    }
    
    /**
     * Gets the default subject for rejected user notifications based on activity and state
     */
    private String getDefaultRejectedUserSubject(String activityId, String state) {
        if (activityId == null) {
            return "Appeal Filed for Rejected Application";
        }
        
        switch (activityId) {
            case "serviceTask_NotifyAllParties":
                return "Appeal Filed for Rejected Application";
            case "serviceTask_NotifyClosure":
                return "Appeal Closed - Application Resubmitted";
            case "serviceTask_NotifyRejection":
                return "Appeal Rejected by Manager";
            default:
                return "Appeal Status Update";
        }
    }
}