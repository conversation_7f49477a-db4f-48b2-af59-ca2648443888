package bw.org.hrdc.weblogic.workplacelearning.scheduler;

import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.service.ncbsc.NCBSCApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Scheduler for NCBSC application renewal notifications
 * Runs daily to check for applications that are 6 months before their 3-year expiry
 */
@Component
@Slf4j
public class RenewalNotificationScheduler {

    @Autowired
    private NCBSCApplicationService ncbscApplicationService;

    @Autowired
    private NotificationService notificationService;

    /**
     * Runs daily at 9:00 AM to check for applications needing renewal notifications
     * Changed from every 5 minutes to prevent duplicate notifications
     */
    //TODO change back after testing is done
//    @Scheduled(cron = "0 0 1 * * *", zone = "Africa/Gaborone")
    @Scheduled(cron = "0 */5 * * * *", zone = "Africa/Gaborone")

    public void checkApplicationsForRenewal() {
        log.info("Starting daily NCBSC application renewal check at {}", LocalDateTime.now());
        
        try {
            List<NCBSCApplication> applicationsNeedingRenewal = ncbscApplicationService.findApplicationsNeedingRenewalNotification();
            
            log.info("Found {} applications needing renewal notification", applicationsNeedingRenewal.size());
            
            int successCount = 0;
            int failureCount = 0;
            
            for (NCBSCApplication application : applicationsNeedingRenewal) {
                boolean notificationSent = sendRenewalNotification(application);
                if (notificationSent) {
                    successCount++;
                } else {
                    failureCount++;
                }
            }
            
            log.info("Completed daily NCBSC application renewal check. Successfully processed: {}, Failed: {}", 
                    successCount, failureCount);
            
        } catch (Exception e) {
            log.error("Error during daily NCBSC application renewal check: {}", e.getMessage(), e);
        }
    }

    /**
     * Sends renewal notification for a specific application and marks it as sent
     * @param application the application that needs renewal notification
     * @return true if notification was sent successfully, false otherwise
     */
    private boolean sendRenewalNotification(NCBSCApplication application) {
        try {
            LocalDateTime expiryDate = application.getManagerApprovedAt().plusYears(3);
            
            String subject = "Application Renewal Required - " + application.getReferenceNumber();
            String message = String.format(
                "Dear Client,\n" +
                "Your NCBSC application with reference number %s is due for renewal.\n" +
                "Application was approved on: %s\n" +
                "Expiry date: %s\n\n" +
                "Please renew your application before the expiry date.\n",
                application.getReferenceNumber(),
                application.getManagerApprovedAt().toLocalDate(),
                expiryDate.toLocalDate()
            );

            // Send notification to the organization
            notificationService.sendRenewalNotification(
                application.getOrganisationId(),
                application.getReferenceNumber(),
                subject,
                message
            );
            
            // Mark notification as sent in database to prevent duplicates 
            boolean marked = ncbscApplicationService.markRenewalNotificationSentByUuid(application.getUuid());
            
            if (marked) {
                log.info("Renewal notification sent and marked for application: {} (expires: {})", 
                        application.getReferenceNumber(), expiryDate.toLocalDate());
                return true;
            } else {
                log.error("Failed to mark renewal notification as sent for application: {}", 
                        application.getReferenceNumber());
                return false;
            }
            
        } catch (Exception e) {
            log.error("Error sending renewal notification for application {}: {}", 
                application.getReferenceNumber(), e.getMessage(), e);
            return false;
        }
    }
}