package bw.org.hrdc.weblogic.workplacelearning.repository;

import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for PreApprovalApplication entity.
 */
@Repository
public interface PreApprovalApplicationRepository extends JpaRepository<PreApprovalApplication, UUID>, 
    JpaSpecificationExecutor<PreApprovalApplication>, PreApprovalApplicationRepositoryCustom {

    /**
     * Find applications by the status of the request.
     *
     * @param status the status of the application (e.g., NEW, APPROVED, REJECTED).
     * @return a list of applications matching the given status.
     */
    List<PreApprovalApplication> findByStatus(String status);

    /**
     * Find applications submitted by a specific user.
     *
     * @param userId the ID of the user who submitted the application.
     * @return a list of applications submitted by the given user.
     */
    List<PreApprovalApplication> findByUserId(UUID userId);

    /**
     * Find applications associated with a specific organization.
     *
     * @param organisationId the ID of the organization.
     * @return a list of applications linked to the given organization.
     */
    List<PreApprovalApplication> findByOrganisationId(UUID organisationId);

    /**
     * Find applications submitted within a specific date range.
     *
     * @param startDate the start date of the range.
     * @param endDate   the end date of the range.
     * @return a list of applications submitted between the given dates.
     */
    List<PreApprovalApplication> findByCreatedDateBetween(java.util.Date startDate, java.util.Date endDate);

    /**
     * Find applications with a specific training provider.
     *
     * @param trainingProvider the name of the training provider.
     * @return a list of applications linked to the given training provider.
     */
    List<PreApprovalApplication> findByTrainingProvider(String trainingProvider);

    /**
     * Find applications by reason for training containing a specific keyword.
     *
     * @param keyword the keyword to search in the reason for training.
     * @return a list of applications where the reason for training contains the keyword.
     */
    List<PreApprovalApplication> findByReasonForTrainingContaining(String keyword);

    /**
     * Find an application by its VAT number.
     *
     * @param vatNumber the VAT number of the application
     * @return Optional containing the application if found
     */
    Optional<PreApprovalApplication> findByVatNumber(String vatNumber);

    /**
     * Find an application by its reference number.
     *
     * @param referenceNumber the reference number of the application
     * @return Optional containing the application if found
     */
    Optional<PreApprovalApplication> findByReferenceNumber(String referenceNumber);
    

    @Modifying
    @Transactional
    @Query("UPDATE PreApprovalApplication a SET " +
            "a.lastModifiedDate = CURRENT_TIMESTAMP, " +
            "a.assignedAgent = CASE WHEN :role = 'AGENT' THEN :userId ELSE a.assignedAgent END, " +
            "a.assignedAgentLead = CASE WHEN :role = 'AGENT_LEAD' THEN :userId ELSE a.assignedAgentLead END, " +
            "a.assignedOfficer = CASE WHEN :role = 'OFFICER' THEN :userId ELSE a.assignedOfficer END, " +
            "a.assignedOfficerLead = CASE WHEN :role = 'OFFICER_LEAD' THEN :userId ELSE a.assignedOfficerLead END, " +
            "a.assignedManager = CASE WHEN :role = 'MANAGER' THEN :userId ELSE a.assignedManager END, " +
            "a.state = " +
            "   CASE " +
            "       WHEN :role = 'AGENT' THEN 'IN_PROCESSING' " +
            "       WHEN :role = 'AGENT_LEAD' THEN 'SUBMITTED' " +
            "       WHEN :role = 'OFFICER' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'OFFICER_LEAD' THEN 'IN_REVIEW' " +
            "       WHEN :role = 'MANAGER' THEN 'IN_APPROVAL' " +
            "       ELSE a.state " +
            "   END, " +
            "a.status = 'PENDING' " +
            "WHERE a.id = :id")
    int updateApplicationAssignedUser(@Param("id") UUID id, @Param("role") String role, @Param("userId") String userId);

    @Modifying
    @Transactional
    @Query("UPDATE PreApprovalApplication a " +
            "SET a.assignedOfficerLead = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE a.assignedOfficerLead " +
            "END, " +
            "a.state = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'IN_REVIEW' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'IN_APPROVAL' " +
            "    ELSE a.state " +
            "END, " +
            "a.status = CASE " +
            "    WHEN :role = 'AGENT' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN 'PENDING' " +
            "    ELSE :action " +
            "END, " +
            "a.assignedManager = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'APPROVED' THEN :newAssignee " +
            "    ELSE a.assignedManager " +
            "END, " +
            "a.assignedAgent = CASE " +
            "    WHEN :role = 'OFFICER' AND :action = 'CHANGE_REQUEST' THEN :newAssignee " +
            "    ELSE a.assignedAgent " +
            "END, " +
            "a.lastModifiedDate = CURRENT_TIMESTAMP " +
            "WHERE a.id = :applicationId")
    int changeApplicationStatus(
            @Param("applicationId") UUID applicationId,
            @Param("role") String role,
            @Param("action") String action,
            @Param("newAssignee") String newAssignee);

    /**
     * Find all applications based on user role and ID.
     * For AGENT_LEAD and OFFICER_LEAD roles, returns all non-deleted applications.
     * For AGENT, OFFICER, and MANAGER roles, returns applications where the user is assigned.
     *
     * @param role the role of the user
     * @param userId the ID of the user
     * @return list of applications matching the criteria
     */
    List<PreApprovalApplication> findAll(Specification<PreApprovalApplication> specification);

    @Query("SELECT COUNT(a) > 0 FROM PreApprovalApplication a " +
            "WHERE a.organisationId = :organisationId " +
            "AND a.status NOT IN (:excludedStatuses) " +
            "AND a.deleted = false")
    boolean existsByOrganisationIdAndStatusNotIn(
            @Param("organisationId") UUID organisationId,
            @Param("excludedStatuses") List<String> excludedStatuses
    );

}
