package bw.org.hrdc.weblogic.workplacelearning.autoassign;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.workSkillsTraining.WorkPlaceTrainingPlanRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.NCBSCApplicationRepo;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc.NOCApplicationRepo;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.ComplaintService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.RequiredArgsConstructor;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
@RequiredArgsConstructor
public class AutoAssignService {

    private static final Logger logger = LoggerFactory.getLogger(ComplaintService.class);
    private final ComplaintRepository complaintRepository;
    private final WorkPlaceTrainingPlanRepository workPlaceTrainingPlanRepository;
    private final NCBSCApplicationRepo ncbscApplicationRepo;
    private final NOCApplicationRepo nocApplicationRepo;


    @Autowired
    private CompanyClient companyClient;

    public String assignAgentByWorkload(String applicationType, String actionBy) {
        try {
            logger.info("Attempting to fetch agents from Account Service for load balancing...");
            
            ApiResponse<?> response = companyClient.fetchAllActiveUsers(actionBy.toLowerCase());
            logger.info("Account Service response - Status: {}, Data: {}", response.isStatus(), response.getData());

            if (response.isStatus() && response.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> agentData = (List<Map<String, Object>>) response.getData();
                logger.info("Found {} agents from Account Service", agentData.size());

                if (!agentData.isEmpty()) {
                    String selectedAgentId = findAgentWithLeastWorkload(agentData , applicationType,actionBy );
                    logger.info("Assigned complaint to agent with least workload: {}", selectedAgentId);
                    return selectedAgentId;
                }
            }
            logger.error("No agents available for assignment from Account Service");
        } catch (Exception e) {
            logger.error("Failed to fetch agents for assignment: {}", e.getMessage());
        }

        return null;
    }

    public String findAgentWithLeastWorkload(List<Map<String, Object>> agents,String applicationType , String actionBy) {

        List<Object[]> workloadData = null;

        if (Enums.ApplicationType.COMPLAINTS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.AGENT.name().equalsIgnoreCase(actionBy)) {

            workloadData = complaintRepository.getComplaintCountByAgent();
            
        } else if (Enums.ApplicationType.APPEALS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.MANAGER.name().equalsIgnoreCase(actionBy)) {

            workloadData = complaintRepository.getAppealCountByManager();
            
        } else if (Enums.ApplicationType.COMPLAINTS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.AGENT_LEAD.name().equalsIgnoreCase(actionBy)) {

            workloadData = complaintRepository.getEscalatedComplaintCountByAgentLead();

        } else if (Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.AGENT.name().equalsIgnoreCase(actionBy)) {

            workloadData = workPlaceTrainingPlanRepository.getPendingSubmittedApplicationsByAgent();

        } else if (Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.OFFICER.name().equalsIgnoreCase(actionBy)) {

            workloadData = workPlaceTrainingPlanRepository.getInreviewApplicationsByOfficer();

        } else if (Enums.ApplicationType.WORK_SKILLS.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.MANAGER.name().equalsIgnoreCase(actionBy)) {

            workloadData = workPlaceTrainingPlanRepository.getInApprovalApplicationsByManager();

        } else if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.AGENT.name().equalsIgnoreCase(actionBy)) {

            workloadData = ncbscApplicationRepo.getPendingApplicationsByAgent();

        } else if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.OFFICER.name().equalsIgnoreCase(actionBy)) {

            workloadData = ncbscApplicationRepo.getInReviewApplicationsByOfficer();

        } else if (Enums.ApplicationType.RECOGNITION.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.MANAGER.name().equalsIgnoreCase(actionBy)) {

            workloadData = ncbscApplicationRepo.getInApprovalApplicationsByManager();

        } else if (Enums.ApplicationType.NOC.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.AGENT.name().equalsIgnoreCase(actionBy)) {

            workloadData = nocApplicationRepo.getPendingApplicationsByAgent();

        } else if (Enums.ApplicationType.NOC.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.OFFICER.name().equalsIgnoreCase(actionBy)) {

            workloadData = nocApplicationRepo.getInReviewApplicationsByOfficer();

        } else if (Enums.ApplicationType.NOC.name().equalsIgnoreCase(applicationType) && Enums.UserRoles.MANAGER.name().equalsIgnoreCase(actionBy)) {

            workloadData = nocApplicationRepo.getInApprovalApplicationsByManager();

        }
         else {

            logger.error("Unsupported application type: {}", applicationType);
            return null;
        }
        
        Map<String, Long> workloadMap = workloadData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // assignee (agent ID)
                row -> ((Number) row[1]).longValue()  // complaint count
            ));

        logger.info("Current agent workloads: {}", workloadMap);

        // Find agent with minimum workload (including agents with 0 complaints)
        String selectedAgent = agents.stream()
            .map(agent -> (String) agent.get("id"))
            .min(Comparator.comparing(agentId -> workloadMap.getOrDefault(agentId, 0L)))
            .orElse(null);

        Long selectedAgentWorkload = workloadMap.getOrDefault(selectedAgent, 0L);
        logger.info("Selected agent {} with {} active complaints", selectedAgent, selectedAgentWorkload);

        return selectedAgent;
    }

}
