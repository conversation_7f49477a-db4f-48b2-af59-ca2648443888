package bw.org.hrdc.weblogic.workplacelearning.service.ncbsc;

import bw.org.hrdc.weblogic.workplacelearning.dto.document.FileDocumentDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.requestForChange.NOCDto;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.noc.NOCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.NCBSCApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.ncbsc.recognition.ShortCourseInformation;
import bw.org.hrdc.weblogic.workplacelearning.mapper.ncbsc.noc.NOCDtoApplicationMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc.NCBSCChangeRequestRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc.NOCApplicationRepo;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.noc.NOCApplicationSpecifications;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.CourseContentDeliveryRepo;
import bw.org.hrdc.weblogic.workplacelearning.repository.ncbsc.recognition.ScopeOfAccreditationRepo;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.autoassign.AutoAssignService;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @CreatedOn 14/04/25 10:34
 * @UpdatedBy martinspectre
 * @UpdatedOn 14/04/25 10:34
 */

@Service
public class NOCApplicationService {

    @Autowired
    private NCBSCChangeRequestRepository repository;

    @Autowired
    private NOCApplicationRepo nocApplicationRepo;

    @Autowired
    private NCBSCApplicationService applicationService;
    @Autowired
    private CourseContentDeliveryRepo courseContentDeliveryRepo;
    @Autowired
    private ScopeOfAccreditationRepo scopeOfAccreditationRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private CompanyClient companyClient;

    @Autowired
    private AutoAssignService autoAssignService;

    private static final Logger logger = LoggerFactory.getLogger(NOCApplicationService.class);

    public NOCApplication create(NOCApplication application) throws Exception {

        return repository.save(application);
    }

    public Page<NOCApplication> getAllApplications(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("dateSubmitted").descending());
        return repository.findAll(pageable);
    }

    public Page<Map<String, Object>> getByOrganisationId(String orgId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("dateSubmitted").descending());

        Specification<NOCApplication> spec = Specification
                .where(NOCApplicationSpecifications.byOrganisationId(orgId))
                .and(NOCApplicationSpecifications.isNotDeleted());

        ApiResponse<?> apiResponse;
        List<Map<String, Object>> companyData = List.of();

        try {
            apiResponse = companyClient.fetchAllCompanies();
            if (apiResponse.isStatus() && apiResponse.getData() != null) {
                companyData = (List<Map<String, Object>>) apiResponse.getData();
            }
        } catch (Exception exception) {
            companyData = List.of();
        }

        List<Map<String, Object>> finalCompanyData = companyData;

        Page<NOCApplication> applications = nocApplicationRepo.findAll(spec, pageable);

        return applications.map(app -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", app.getUuid());
            map.put("referenceNumber", app.getReferenceNumber());
            map.put("applicationNumber", app.getApplicationNumber());
            map.put("organisationId", app.getOrganisationId());
            map.put("shortCourseDeliveryMode", app.getShortCourseDeliveryMode());
            map.put("dateSubmitted", app.getDateSubmitted());
            map.put("applicationStatus", app.getApplicationStatus());
            map.put("applicationState", app.getApplicationState());
            map.put("isMajorChange", app.getIsMajorChange());

            String courseTitle = app.getShortCourseInformation() != null && app.getShortCourseInformation().getTitle() != null
                    ? app.getShortCourseInformation().getTitle()
                    : "";
            map.put("courseTitle", courseTitle);

            if (app.getApplicationState() == Enums.State.IN_APPROVAL && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedManager());
            } else if (app.getApplicationState() == Enums.State.IN_REVIEW && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedOfficer());
            } else if (app.getApplicationState() == Enums.State.IN_PROCESSING && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedAgent());
            } else if (app.getApplicationState() == Enums.State.SUBMITTED && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedAgentLead());
            } else {
                map.put("assignedTo", null);
            }

            finalCompanyData.stream()
                    .filter(company -> company != null && orgId.equals(company.get("uuid")))
                    .findFirst()
                    .ifPresent(company -> map.put("companyName", company.get("name")));

            return map;
        });
    }

    public @NonNull Optional<NOCApplication> getApplicationByReference(String referenceNumber) {
        Specification<NOCApplication> spec = Specification
                .where(NOCApplicationSpecifications.isNotDeleted())
                .and((root, query, cb) -> cb.equal(cb.lower(root.get("referenceNumber")), referenceNumber.toLowerCase()));

        return nocApplicationRepo.findOne(spec);
    }

    public Page<NOCApplication> searchApplications(Enums.Status status, Enums.ChangeSeverity severity, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);

        Specification<NOCApplication> spec = Specification.where(null);

        if (status != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("applicationStatus"), status));
        }

        if (severity != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("severity"), severity));
        }

        return repository.findAll(spec, pageable);

    }

    public Optional<NOCApplication> getById(String id) {
        return nocApplicationRepo.findById(id);
    }

    @Transactional
    public Optional<NOCApplication> update(String id, NOCApplication updated)
    {
        try{
            return nocApplicationRepo.findById(id).map(existing -> {
                updated.setId(existing.getId());
                updated.setUuid(existing.getUuid());
                //BeanUtils.copyProperties(updated, existing, "id");

                existing.setRecognitionNumber(updated.getRecognitionNumber());
                existing.setTrainingNeedsAssessmentPurpose(updated.getTrainingNeedsAssessmentPurpose());
                existing.setTrainingNeedsAssessmentSkillsNeedsAnalysis(updated.getTrainingNeedsAssessmentSkillsNeedsAnalysis());
                existing.setShortCourseDeliveryMode(updated.getShortCourseDeliveryMode());
                existing.setKeyFacilitation(updated.getKeyFacilitation());
                existing.setAssessmentType(updated.getAssessmentType());
                existing.setCertification(updated.getCertification());
                existing.setThirdPartyArrangements(updated.getThirdPartyArrangements());
                existing.setResources(updated.getResources());
                existing.setShortCourseEndorsement(updated.getShortCourseEndorsement());
                existing.setApplicationStatus(updated.getApplicationStatus());
                existing.setApplicationState(updated.getApplicationState());
                existing.setSeverity(updated.getSeverity());
                existing.setShortCourseInformation(updated.getShortCourseInformation());
                existing.setScopeOfAccreditation(updated.getScopeOfAccreditation());
                existing.setCourseContentAndDelivery(updated.getCourseContentAndDelivery());
                existing.setJustification(updated.getJustification());
                existing.setReferenceNumber(updated.getReferenceNumber());
                return repository.save(existing);
            });
        }catch(Exception exception){
            logger.error("Error Updating NOC Applications exception: {}", exception.getMessage());
            return Optional.empty();
        }
    }

    public boolean delete(String id) {
        return nocApplicationRepo.deleteById(id) > 0;
    }

    @Transactional
    public boolean updateApplicationAssignedUser(String referenceNumber, Enums.UserRoles role, String userId) {
        Specification<NOCApplication> spec = Specification
                .where(NOCApplicationSpecifications.isNotDeleted())
                .and(NOCApplicationSpecifications.byReferenceNumber(referenceNumber));

        Optional<NOCApplication> optionalApplication = nocApplicationRepo.findOne(spec);

        if (optionalApplication.isEmpty()) {
            return false;
        }

        NOCApplication application = optionalApplication.get();

        switch (role) {
            case AGENT -> {
                application.setAssignedAgent(userId);
                application.setApplicationState(Enums.State.valueOf("IN_PROCESSING"));
            }
            case AGENT_LEAD -> {
                application.setAssignedAgentLead(userId);
                application.setApplicationState(Enums.State.valueOf("SUBMITTED"));
            }
            case OFFICER -> {
                application.setAssignedOfficer(userId);
                application.setApplicationState(Enums.State.valueOf("IN_REVIEW"));
            }
            case OFFICER_LEAD -> {
                application.setAssignedOfficerLead(userId);
                application.setApplicationState(Enums.State.valueOf("IN_REVIEW"));
            }
            case MANAGER -> {
                application.setAssignedManager(userId);
                application.setApplicationState(Enums.State.valueOf("IN_APPROVAL"));
            }
            default -> {
                // Do nothing or log if role is unexpected
            }
        }

        application.setApplicationStatus(Enums.Status.valueOf("PENDING"));
        application.setUpdatedAt(LocalDateTime.now());

        nocApplicationRepo.save(application);

           if (Enums.UserRoles.AGENT.name().equalsIgnoreCase(role.name())) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.getReferenceNumber(),
                                    application.getId().toString(),
                                    application.getApplicationStatus().toString(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.NOC.name()
                                    
                );
            } else if (Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role.name())) {
                            notificationService.sendNotificationToUser(
                                    userId,
                                    application.getReferenceNumber(),
                                    application.getId().toString(),
                                    application.getApplicationStatus().toString(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.NOC.name()
                );
            } 

        return true;
    }

    @Transactional
    public boolean updateApplicationStatus(NOCApplication application, Enums.UserRoles role, String action, String newAssignee) {
        boolean changed = false;

        if ("AGENT".equals(role.name()) && "APPROVED".equals(action)) {
            application.setApplicationState(Enums.State.valueOf("IN_REVIEW"));
            application.setApplicationStatus(Enums.Status.valueOf("PENDING"));
            changed = true;
        }

        if ("OFFICER".equals(role.name())) {
            if ("APPROVED".equals(action)) {
                application.setApplicationState(Enums.State.valueOf("IN_APPROVAL"));
                application.setApplicationStatus(Enums.Status.valueOf("PENDING"));
                changed = true;
            } else if ("CHANGE_REQUEST".equals(action)) {
                changed = true;
            }
        }

        if (!changed) {
            application.setApplicationStatus(Enums.Status.valueOf(action));
        }

        nocApplicationRepo.save(application);
        return true;
    }

    @Transactional
    private void mergeApplication(NOCApplication app) {
        if (app.getApplicationState() == Enums.State.IN_APPROVAL &&
                app.getApplicationStatus() == Enums.Status.APPROVED) {

            Optional<NCBSCApplication> application = applicationService.getApplicationById(app.getRecognitionNumber());
            if(application.isPresent()){
                NCBSCApplication applicationSaved = application.get();
                ShortCourseInformation sci = app.getShortCourseInformation();
                if (sci != null && (sci.getUuid() != null &&!sci.getUuid().isEmpty())) {
                    sci.setApplication(applicationSaved);
                    applicationService.updateShortCourseInformation(sci);
                }

//                CourseContentDelivery ccd = app.getCourseContentAndDelivery();
//                if ((ccd != null && (ccd.getUuid() != null &&!ccd.getUuid().isEmpty()))) {
//                    applicationService.updateCourseContentAndDelivery(app.getRecognitionNumber(), application.get(), null, app);
//                }

                if (app.getScopeOfAccreditation() != null) {
                    applicationService.updateScopeOfAccreditation(app.getRecognitionNumber(), app.getScopeOfAccreditation());
                }
            }
        }
    }

    public Page<Map<String, Object>> getAllApplications(PageRequest pageable, String assignedTo, String state) {
        ApiResponse<?> apiResponse;
        List<Map<String, Object>> res = List.of();

        try {
            apiResponse = companyClient.fetchAllCompanies();
            if (apiResponse.isStatus() && apiResponse.getData() != null) {
                res = (List<Map<String, Object>>) apiResponse.getData();
            }
        } catch (Exception exception) {
            res = List.of();
        }

        List<Map<String, Object>> finalRes = res;

        Specification<NOCApplication> spec = Specification
                .where(NOCApplicationSpecifications.isNotDeleted())
                .and(state != null && !state.isEmpty() ? NOCApplicationSpecifications.byState(state) : null)
                .and(assignedTo != null && !assignedTo.isEmpty() ? NOCApplicationSpecifications.byAssignedTo(assignedTo) : null);

        // Determine assignedTo based on state

        return nocApplicationRepo.findAll(spec, pageable).map(app -> {
            Map<String, Object> map = new HashMap<>();

            map.put("id", app.getUuid());
            map.put("referenceNumber", app.getReferenceNumber());
            map.put("applicationNumber", app.getApplicationNumber());
            map.put("organisationId", app.getOrganisationId());
            map.put("shortCourseDeliveryMode", app.getShortCourseDeliveryMode());
            map.put("dateSubmitted", app.getDateSubmitted());
            map.put("applicationStatus", app.getApplicationStatus());
            map.put("applicationState", app.getApplicationState());
            map.put("isMajorChange", app.getIsMajorChange());

            String courseTitle = app.getShortCourseInformation() != null && app.getShortCourseInformation().getTitle() != null
                    ? app.getShortCourseInformation().getTitle()
                    : "";
            map.put("courseTitle", courseTitle);

            // Determine assignedTo based on state
            if (app.getApplicationState() == Enums.State.IN_APPROVAL && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedManager());
            } else if (app.getApplicationState() == Enums.State.IN_REVIEW && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedOfficer());
            } else if (app.getApplicationState() == Enums.State.IN_PROCESSING && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedAgent());
            } else if (app.getApplicationState() == Enums.State.SUBMITTED && app.getApplicationStatus() != Enums.Status.CHANGE_REQUEST) {
                map.put("assignedTo", app.getAssignedAgentLead());
            } else {
                map.put("assignedTo", null);
            }

            List<Map<String, Object>> company = finalRes.stream()
                    .filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId()))
                    .toList();
            if (!company.isEmpty()) {
                map.put("companyName", company.get(0).get("name"));
            }

            return map;
        });
    }

    public @NonNull Optional<NOCApplication> getByApplicationNumber(String applicationNumber) {
        return nocApplicationRepo.fetchByApplicationNumber(applicationNumber);
    }

    public void updateProcessInstanceId(String referenceid, String processInstanceId) {
        try {
            // First try to update using JPA
            Optional<NOCApplication> applicationOpt = getApplicationByReference(referenceid);
            if (applicationOpt.isPresent()) {
                try {
                    NOCApplication application = applicationOpt.get();
                    application.setProcessInstanceId(processInstanceId);
                    nocApplicationRepo.save(application);
                    logger.info("Process instance ID {} updated for application {}", processInstanceId, referenceid);
                    return; // Exit if successful
                } catch (Exception e) {
                    // If JPA update fails, log it and continue to the fallback
                    logger.warn("Error updating process instance ID using JPA: {}", e.getMessage());
                }
            } else {
                logger.warn("Could not find NOC application with reference number {} to update process instance ID", referenceid);
                return; // Exit if application not found
            }
            
            // Fallback: Use native query to bypass auditing
            try {
                int updated = nocApplicationRepo.updateProcessInstanceIdNative(processInstanceId, referenceid);
                if (updated > 0) {
                    logger.info("Process instance ID {} updated for application {} using native query", 
                        processInstanceId, referenceid);
                } else {
                    logger.warn("Native query did not update any rows for application {}", referenceid);
                }
            } catch (Exception sqlEx) {
                logger.error("Failed to update process instance ID using native query: {}", sqlEx.getMessage());
                
                // Last resort: Log that the process was started successfully even if we couldn't save the ID
                logger.info("Workflow process started successfully for application {} but could not save process instance ID {}",
                    referenceid, processInstanceId);
            }
        } catch (Exception e) {
            // Catch any other unexpected errors
            logger.error("Unexpected error updating process instance ID for application {}: {}", 
                referenceid, e.getMessage(), e);
        }
    }

    public NOCApplication fullApplicationToEntity(NOCDto fullApplication){
        return NOCDtoApplicationMapper.toEntity(fullApplication);
    }

    public NOCDto fullLApplicationToDto(NOCApplication application, List<FileDocumentDto> files){
        return NOCDtoApplicationMapper.toDto(application,files);
    }

    public boolean isDuplicateChangeRequest(String recognitionApplicationId) {
        return repository.isNOCExistForRecognition(
                recognitionApplicationId,
                List.of(Enums.Status.APPROVED, Enums.Status.REJECTED)
        );
    }

    /**
     * Assigns agent by workload for NOC applications
     */
    public String assignAgentByWorkload(String applicationType, String role) {
        return autoAssignService.assignAgentByWorkload(applicationType, role);
    }

    /**
     * Auto-assigns agent, officer, or manager to NOC application based on workload
     * Similar to NCBSC auto-assignment functionality
     */
    @Transactional
    public void updateAutoassignedAgent(String applicationId, String autoAssignRole) {
        try {
            logger.info("Auto-assigning agent for NOC application ID: {}, role: {}", applicationId, autoAssignRole);

            if (autoAssignRole == null || autoAssignRole.trim().isEmpty()) {
                logger.warn("Auto assign role is null or empty for NOC application ID: {}", applicationId);
                return;
            }

            String assignedAgent = autoAssignService.assignAgentByWorkload(Enums.ApplicationType.NOC.name(), autoAssignRole);

            if (assignedAgent == null || assignedAgent.trim().isEmpty()) {
                logger.warn("No agent assigned for NOC application ID: {}, role: {}", applicationId, autoAssignRole);
                return;
            }

            Optional<NOCApplication> applicationOpt = getById(applicationId);
            if (applicationOpt.isEmpty()) {
                logger.warn("NOC application not found with ID: {}", applicationId);
                return;
            }

            NOCApplication application = applicationOpt.get();
            String referenceNumber = application.getReferenceNumber();

            if ("OFFICER".equalsIgnoreCase(autoAssignRole)) {
                nocApplicationRepo.updateApplicationAssignedUser(referenceNumber, Enums.UserRoles.OFFICER.name(), assignedAgent);
                logger.info("Assigned officer {} to NOC application ID: {}", assignedAgent, applicationId);

            } else if ("MANAGER".equalsIgnoreCase(autoAssignRole)) {
                nocApplicationRepo.updateApplicationAssignedUser(referenceNumber, Enums.UserRoles.MANAGER.name(), assignedAgent);
                logger.info("Assigned manager {} to NOC application ID: {}", assignedAgent, applicationId);

            } else {
                logger.warn("Unsupported role for auto-assignment: {}", autoAssignRole);
                return;
            }

        } catch (Exception e) {
            logger.error("Error auto-assigning agent for NOC application ID: {}, role: {}, error: {}",
                     applicationId, autoAssignRole, e.getMessage(), e);
            // Don't rethrow the exception to prevent the main transaction from failing
        }
    }
}
