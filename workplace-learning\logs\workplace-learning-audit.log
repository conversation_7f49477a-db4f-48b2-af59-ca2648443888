2025-07-15T11:02:04.843+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 14936 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:04.860+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:02:09.639+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:10.929+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1269 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:12.124+05:30  INFO 14936 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:02:13.863+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.873+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.882+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.887+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.892+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:14.464+05:30  INFO 14936 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:14.682+05:30  INFO 14936 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:14.788+05:30  INFO 14936 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:15.643+05:30  INFO 14936 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:02:15.715+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:16.155+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@47cf65f1
2025-07-15T11:02:16.158+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:20.228+05:30  INFO 14936 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:02:21.046+05:30  INFO 14936 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:21.710+05:30  INFO 14936 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:02:23.862+05:30  WARN 14936 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:02:24.172+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:25.245+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:28.453+05:30  INFO 14936 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:02:29.768+05:30  INFO 14936 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:02:29.840+05:30  WARN 14936 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:02:29.979+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:02:30.028+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:02:30.038+05:30  INFO 14936 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:02:30.511+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:02:30.515+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:02:30.518+05:30  INFO 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:02:30.521+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557550519 with initial instances count: 5
2025-07-15T11:02:30.528+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:02:30.529+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557550529, current=UP, previous=STARTING]
2025-07-15T11:02:30.530+05:30  WARN 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:30.577+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 26.29 seconds (process running for 29.607)
2025-07-15T11:02:30.580+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:02:30.658+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557551492, current=DOWN, previous=UP]
2025-07-15T11:02:31.494+05:30  WARN 14936 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:31.533+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:31.535+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:02:31.542+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:02:31.543+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:02:34.558+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:02:34.576+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:02:34.577+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:02:52.850+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:52.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:02:54.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:55.187+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 324 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:55.652+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:02:56.193+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.198+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.205+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.211+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.215+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.720+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3863 ms
2025-07-15T11:02:56.989+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:57.294+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@56a012e1
2025-07-15T11:02:57.298+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:57.309+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:02:57.649+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:57.717+05:30  INFO 16564 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:57.770+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:58.132+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:03:00.450+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:03:01.107+05:30  INFO 16564 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:03:01.461+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:03:02.615+05:30  WARN 16564 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:03:02.703+05:30  WARN 16564 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:03:02.779+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:03.334+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:05.449+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:03:06.606+05:30  INFO 16564 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:03:06.669+05:30  WARN 16564 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:03:06.757+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:03:06.783+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:03:06.787+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:03:06.796+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:03:06.799+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:03:07.093+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:03:07.095+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:03:07.097+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:03:07.099+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557587098 with initial instances count: 5
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557587110, current=UP, previous=STARTING]
2025-07-15T11:03:07.111+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:03:07.112+05:30  WARN 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:03:07.131+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:03:07.133+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 14.98 seconds (process running for 15.824)
2025-07-15T11:05:00.021+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:05:00.021928800
2025-07-15T11:05:00.047+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:05:00.047475300 to 2023-01-15T11:05:00.047475300
2025-07-15T11:05:01.630+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:05:01.644+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:08:06.809+05:30  INFO 16564 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:08:39.171+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:08:39.173+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:08:39.177+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-15T11:08:39.310+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.311+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.573+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:09:35.547+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:09:35.613+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:35.643+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-33D049 has been updated successfully
2025-07-15T11:09:43.559+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:43.572+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:43.580+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:43.607+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:46.164+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:46.165+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:46.242+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:46.243+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:46.254+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:46.255+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-********-08BF5C
2025-07-15T11:09:46.271+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:09:57.930+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:57.949+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:57.950+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:57.962+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:58.476+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:58.478+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:58.483+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:58.484+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:58.485+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:58.486+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-********-3184AE
2025-07-15T11:09:58.489+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:10:00.007086300
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:10:00.007086300 to 2023-01-15T11:10:00.007086300
2025-07-15T11:10:00.017+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:10:00.018+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:10:52.959+05:30  INFO 16564 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:10:52.971+05:30  INFO 16564 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:10:52.977+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052977, current=DOWN, previous=UP]
2025-07-15T11:10:52.991+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052991, current=UP, previous=DOWN]
2025-07-15T11:10:53.045+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:10:53.220+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:10:53.477+05:30  INFO 16564 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:10:53.569+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:10:53.670+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:10:53.685+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:10:56.702+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:10:56.731+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:10:56.735+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:10:57.403+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:10:58.057+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:10:58.061+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:11:00.903+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:11:01.158+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 254 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:23.325+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 12912 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:16:23.328+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:16:26.723+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:16:27.267+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 529 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:28.076+05:30  INFO 12912 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:16:29.350+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.362+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.379+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.385+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.393+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.976+05:30  INFO 12912 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:16:30.162+05:30  INFO 12912 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:16:30.241+05:30  INFO 12912 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:16:30.756+05:30  INFO 12912 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:16:30.804+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:16:31.092+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@1fa24e7
2025-07-15T11:16:31.094+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:16:34.735+05:30  INFO 12912 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:16:35.365+05:30  INFO 12912 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:35.925+05:30  INFO 12912 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:16:38.012+05:30  WARN 12912 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:16:38.413+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:39.506+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:42.676+05:30  INFO 12912 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:16:44.109+05:30  INFO 12912 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:16:44.177+05:30  WARN 12912 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:16:44.289+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:16:44.335+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:16:44.344+05:30  INFO 12912 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:16:44.838+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:16:44.840+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:16:44.842+05:30  INFO 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:16:44.844+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558404844 with initial instances count: 5
2025-07-15T11:16:44.854+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:16:44.855+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558404855, current=UP, previous=STARTING]
2025-07-15T11:16:44.857+05:30  WARN 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:44.905+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:16:44.910+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 22.626 seconds (process running for 24.666)
2025-07-15T11:16:44.976+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:16:46.134+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:16:46.135+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558406135, current=DOWN, previous=UP]
2025-07-15T11:16:46.136+05:30  WARN 12912 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:46.208+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:46.215+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:16:46.232+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:16:46.234+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:16:49.241+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:16:49.261+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:16:49.262+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:17:24.444+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:17:24.453+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:17:24.913+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:17:24.915+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:17:32.056+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:17:33.268+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1181 ms. Found 31 JPA repository interfaces.
2025-07-15T11:17:34.945+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:17:37.045+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.060+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.082+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.097+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.117+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:39.332+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:17:39.390+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:17:39.393+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:17:39.715+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:17:39.719+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 14797 ms
2025-07-15T11:17:40.545+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:17:41.316+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@78195ee2
2025-07-15T11:17:41.329+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:17:41.381+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:17:42.493+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:17:42.637+05:30  INFO 26816 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:17:42.707+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:17:43.199+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:17:45.871+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:17:46.456+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:17:46.866+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:17:48.196+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:17:48.294+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:17:48.373+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:49.054+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:51.385+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:17:52.530+05:30  INFO 26816 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:17:52.592+05:30  WARN 26816 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:17:52.685+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:17:52.725+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:17:52.731+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:17:52.740+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:17:52.742+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:17:52.744+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:17:53.086+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:17:53.088+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:17:53.090+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:17:53.092+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558473091 with initial instances count: 5
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558473106, current=UP, previous=STARTING]
2025-07-15T11:17:53.107+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:17:53.107+05:30  WARN 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:17:53.125+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:17:53.127+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:17:53.163+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 31.066 seconds (process running for 33.766)
2025-07-15T11:17:53.164+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:18:57.646+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:18:57.647+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:18:57.652+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:58.054+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:19:59.623+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:19:59.716+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:19:59.755+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-6B6267 has been updated successfully
2025-07-15T11:20:00.018+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:20:00.*********
2025-07-15T11:20:00.022+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:20:00.********* to 2023-01-15T11:20:00.*********
2025-07-15T11:20:00.146+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:20:00.147+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:20:07.819+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:20:07.839+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:20:07.852+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 0c70e3cf-2a5b-48b1-8065-96e0892eb972 has been updated successfully
2025-07-15T11:20:07.881+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:20:09.575+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:20:09.577+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:20:09.734+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:20:09.735+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:20:09.750+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:20:09.753+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : No agent available for auto-assignment to NCBSC application REF-********-D55F0F
2025-07-15T11:22:11.739+05:30  INFO 26816 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 10 class path changes (0 additions, 0 deletions, 10 modifications)
2025-07-15T11:22:11.749+05:30  INFO 26816 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:22:11.765+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731765, current=DOWN, previous=UP]
2025-07-15T11:22:11.780+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731780, current=UP, previous=DOWN]
2025-07-15T11:22:11.784+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:22:11.816+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:22:11.856+05:30  INFO 26816 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:11.866+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:22:11.885+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:22:11.893+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:22:14.897+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:22:14.903+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:22:14.904+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:22:15.060+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:22:15.178+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:22:15.179+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:22:16.355+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:22:16.578+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 221 ms. Found 31 JPA repository interfaces.
2025-07-15T11:22:16.843+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:22:16.980+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.985+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.990+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.994+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.997+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:17.256+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:22:17.337+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:22:17.338+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2154 ms
2025-07-15T11:22:17.513+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:22:17.604+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@68415437
2025-07-15T11:22:17.605+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:22:17.606+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:22:17.902+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:22:17.908+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:22:17.932+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:22:19.490+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:22:20.271+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:21.170+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:22:21.310+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:22:21.409+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:22:22.034+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:19.416+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:27:19.426+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:27:19.759+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:27:19.761+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:27:28.973+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:27:30.404+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1393 ms. Found 31 JPA repository interfaces.
2025-07-15T11:27:33.129+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:27:35.520+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:27:35.553+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:35.588+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:27:35.612+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:35.628+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:38.752+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:27:38.837+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:27:38.842+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:27:39.306+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:27:39.309+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 19544 ms
2025-07-15T11:27:40.180+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:27:41.104+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@2b3bea85
2025-07-15T11:27:41.116+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:27:41.181+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:27:42.940+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:27:43.233+05:30  INFO 21912 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:27:43.502+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:27:45.080+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:27:50.722+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:27:51.568+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:27:52.261+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:27:53.797+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:27:53.896+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:27:53.972+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:54.715+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:57.422+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:27:58.700+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:27:58.775+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:27:58.873+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:27:58.900+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:27:58.904+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:27:58.913+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:27:58.914+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:27:58.914+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:27:58.916+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:27:59.249+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:27:59.251+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:27:59.253+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:27:59.255+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559079254 with initial instances count: 5
2025-07-15T11:27:59.267+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559079267, current=UP, previous=STARTING]
2025-07-15T11:27:59.268+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:27:59.273+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:27:59.274+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:27:59.294+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:27:59.295+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:27:59.334+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:27:59.342+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 42.061 seconds (process running for 42.736)
2025-07-15T11:29:12.408+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:29:12.409+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:29:12.412+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-15T11:29:12.534+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:29:12.535+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:29:12.785+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:30:00.010+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:30:00.*********
2025-07-15T11:30:00.011+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:30:00.********* to 2023-01-15T11:30:00.*********
2025-07-15T11:30:00.084+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:30:00.084+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:30:03.304+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:30:03.358+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:30:03.386+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-F68C64 has been updated successfully
2025-07-15T11:30:09.168+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:30:09.176+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:30:09.183+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number f240c561-3d0c-4648-bd91-5d37b73bc8c0 has been updated successfully
2025-07-15T11:30:09.202+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:30:10.782+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:30:10.783+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:30:10.868+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T11:30:10.869+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 0 active complaints
2025-07-15T11:30:10.870+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-15T11:30:10.893+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Auto-assigned agent ad9f2ba3-898c-487f-a40e-893a10fe8477 to NCBSC application REF-********-70EAF4
2025-07-15T11:30:13.389+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Workflow response: {applicationData={courseDeliverySchedule=[{date=2024-01-15T00:00:00.000+00:00, topic=Introduction to Python, hours=4, Id=2397373010388100, Uuid=ca029ec4-f186-4968-829c-c871a325f606}, {date=2024-01-16T00:00:00.000+00:00, topic=Data Types and Variables, hours=3, Id=2397373010730500, Uuid=06f57b2a-628a-4a6f-aa55-0aad5b4b3dcb}], scopeOfAccreditations=[{fieldsOfLearningAccredited=Project Management, Id=2397373003125600, Uuid=043b81bb-421a-4822-96d4-298b5f171917}, {fieldsOfLearningAccredited=Information Technology, Id=2397373003653800, Uuid=7aba0d9b-2fec-460a-87fb-1f2e3f74e720}], attachments=[], application={assignedAgent=ad9f2ba3-898c-487f-a40e-893a10fe8477, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, referenceNumber=REF-********-70EAF4, applicationNumber=APP-********-04DC13, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, trainingNeedsAssessmentPurpose=Identify skill gaps in IT, trainingNeedsAssessmentSkillsNeedsAnalysis=Survey-based analysis of existing workforce skills, shortCourseDeliveryMode=Online, keyFacilitation=Experienced trainers with relevant certifications, assessmentType=Multiple-choice tests and project evaluations, certification=Certificate of completion, thirdPartyArrangements=Collaboration with ABC Institute, resources=Resources needed for the course, shortCourseEndorsement=Course Endorsement, dateSubmitted=2025-07-15T11:29:12.658721, applicationStatus=PENDING, applicationState=IN_PROCESSING, shortCourseInformation={title=Introduction to Sewing, type=Workshop, duration=2 weeks, fieldOfLearning=Computer Science, subFieldOfLearning=Conditional operations, level=NFQ, accreditingBody=HRDC, courseLearningTime=40, startDate=null, endDate=null, yearDueForReview=2025, targetPopulation=University students, entryRequirements=Basic computer literacy, Id=2397373005652300, Uuid=e3371293-6cb1-492a-8c98-c4ac9f0bee31}, courseContentAndDelivery={exitLevelOutcomes=Write basic programs in Python, learningOutcomesSummary=, shortCourseDeliveryMode=, shortCourseDeliveryType=, location=, Id=2397373006807500, Uuid=7cfb1366-c0fb-4f6c-ab89-30a621787046}, processInstanceId=null, managerApprovedAt=null, renewalNotificationSent=false, renewalNotificationSentAt=null, Id=2397372984629400, Uuid=f240c561-3d0c-4648-bd91-5d37b73bc8c0, CreatedAt=2025-07-15T11:29:12.734894, UpdatedAt=2025-07-15T11:30:10.888909, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}, documents=[{key=documents/pop/payment-proof-REF-20250710-A8A5F9.pdf, docName=null, docExt=null, identifier=f240c561-3d0c-4648-bd91-5d37b73bc8c0, fileType=null, docSize=null, fileUrl=null, Id=2397429471452400, Uuid=70c134fc-9161-4b69-9753-fb98c84fd8e8, CreatedAt=2025-07-15T11:30:09.190676, UpdatedAt=2025-07-15T11:30:09.190676, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}], learningOutcomes=[{id=2397373007738000, assessmentCriteria=[{topic=, objective=, deliveryStrategy=, assessmentStrategy=, dateFrom=null, dateTo=null, hours=null, Id=2397373009423600, Uuid=d8839da7-f74c-42a3-8e6f-20d7f33a89e6}], uuid=145c3158-d838-4e3a-95ff-a31d22c1faed, outcome=Understand Python syntax}], quotation={quoteRef=QUO-********-E9A733, reference=f240c561-3d0c-4648-bd91-5d37b73bc8c0, checklistItemsJson=null, totalAmount=5000.0, accepted=true, Id=2397373081663000, Uuid=df95b2f2-f2c1-4d7e-8994-ebc5a2882f9f, CreatedAt=2025-07-15T11:29:12.795066, UpdatedAt=2025-07-15T11:30:03.419967, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}}, success=true, message=Process started and application data retrieved successfully, processInstanceId=f7dbe565-6140-11f0-ae19-00155d4133f2}
2025-07-15T11:30:13.401+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Successfully updated process instance ID f7dbe565-6140-11f0-ae19-00155d4133f2 for application REF-********-70EAF4
2025-07-15T11:30:13.402+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Process instance ID f7dbe565-6140-11f0-ae19-00155d4133f2 saved for application REF-********-70EAF4
2025-07-15T11:32:58.922+05:30  INFO 21912 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:34:53.224+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:34:53.227+05:30  INFO 21912 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:34:53.227+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559493227, current=DOWN, previous=UP]
2025-07-15T11:34:53.230+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559493230, current=UP, previous=DOWN]
2025-07-15T11:34:53.230+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:34:53.235+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:34:53.256+05:30  INFO 21912 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:34:53.259+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:34:53.265+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:34:53.267+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:34:56.271+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:34:56.289+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:34:56.290+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:34:56.405+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:34:56.521+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:34:56.522+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:35:01.144+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:35:02.260+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1112 ms. Found 31 JPA repository interfaces.
2025-07-15T11:35:03.558+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:35:04.258+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:04.327+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:04.442+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:04.476+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:04.489+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:05.947+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:35:05.981+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:35:06.013+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:35:06.510+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:35:06.544+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 10015 ms
2025-07-15T11:35:07.838+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:35:08.502+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6dc18b47
2025-07-15T11:35:08.553+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:35:08.577+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:35:11.034+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:35:11.077+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:35:11.253+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:35:18.855+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:35:22.480+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:26.653+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:35:27.036+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:35:27.308+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:30.060+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:34.744+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:35:36.635+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:35:36.710+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:35:36.841+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:35:36.854+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:35:36.855+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:35:36.856+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:35:36.857+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:35:36.858+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:35:36.858+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:35:36.859+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:35:36.860+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:35:36.860+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:35:36.978+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:35:36.979+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:35:36.979+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:35:36.980+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559536980 with initial instances count: 5
2025-07-15T11:35:36.984+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559536984, current=UP, previous=STARTING]
2025-07-15T11:35:36.984+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:35:37.000+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:35:37.006+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:35:37.009+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:35:37.012+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:35:37.015+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:35:37.052+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 40.633 seconds (process running for 500.447)
2025-07-15T11:35:37.058+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-15T11:35:38.386+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:35:38.391+05:30  INFO 21912 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:35:38.393+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559538393, current=DOWN, previous=UP]
2025-07-15T11:35:38.394+05:30  WARN 21912 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:35:38.455+05:30  INFO 21912 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:38.461+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:35:38.472+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:35:38.481+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:35:41.489+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:35:41.563+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:35:41.564+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:35:41.687+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:35:41.746+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:35:41.747+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:35:42.345+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:35:42.487+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 141 ms. Found 31 JPA repository interfaces.
2025-07-15T11:35:42.637+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:35:42.722+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:42.724+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:42.728+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:42.731+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:42.732+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:43.216+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:35:43.217+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:35:43.217+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:35:43.258+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:35:43.259+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1509 ms
2025-07-15T11:35:43.364+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:35:43.433+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@773e8c6f
2025-07-15T11:35:43.434+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:35:43.434+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:35:43.588+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:35:43.593+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:35:43.613+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:35:48.046+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:35:53.646+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:56.036+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:35:56.376+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:35:56.873+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:58.663+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:36:05.570+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:36:09.318+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:36:09.563+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:36:09.760+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:36:09.794+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:36:09.829+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:36:09.856+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:36:09.904+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:36:09.989+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:36:10.048+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:36:10.071+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:36:10.093+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:36:10.106+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:36:10.342+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:36:10.372+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:36:10.379+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:36:10.397+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559570397 with initial instances count: 5
2025-07-15T11:36:10.450+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:36:10.465+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559570465, current=UP, previous=STARTING]
2025-07-15T11:36:10.497+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:36:10.500+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:36:10.535+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:36:10.676+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:36:10.705+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:36:10.836+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 29.143 seconds (process running for 534.23)
2025-07-15T11:36:10.906+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-15T11:36:11.069+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:36:11.157+05:30  INFO 21912 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:36:11.201+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559571201, current=DOWN, previous=UP]
2025-07-15T11:36:11.237+05:30  WARN 21912 --- [workplace-learning] [Thread-13] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:36:11.299+05:30  INFO 21912 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:36:11.343+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:36:11.420+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:36:11.457+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
