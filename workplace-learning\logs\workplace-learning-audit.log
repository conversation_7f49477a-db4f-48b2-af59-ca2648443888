2025-07-15T11:02:04.843+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 14936 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:04.860+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:02:09.639+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:10.929+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1269 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:12.124+05:30  INFO 14936 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:02:13.863+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.873+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.882+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.887+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.892+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:14.464+05:30  INFO 14936 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:14.682+05:30  INFO 14936 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:14.788+05:30  INFO 14936 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:15.643+05:30  INFO 14936 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:02:15.715+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:16.155+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@47cf65f1
2025-07-15T11:02:16.158+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:20.228+05:30  INFO 14936 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:02:21.046+05:30  INFO 14936 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:21.710+05:30  INFO 14936 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:02:23.862+05:30  WARN 14936 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:02:24.172+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:25.245+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:28.453+05:30  INFO 14936 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:02:29.768+05:30  INFO 14936 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:02:29.840+05:30  WARN 14936 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:02:29.979+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:02:30.028+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:02:30.038+05:30  INFO 14936 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:02:30.511+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:02:30.515+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:02:30.518+05:30  INFO 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:02:30.521+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557550519 with initial instances count: 5
2025-07-15T11:02:30.528+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:02:30.529+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557550529, current=UP, previous=STARTING]
2025-07-15T11:02:30.530+05:30  WARN 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:30.577+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 26.29 seconds (process running for 29.607)
2025-07-15T11:02:30.580+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:02:30.658+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557551492, current=DOWN, previous=UP]
2025-07-15T11:02:31.494+05:30  WARN 14936 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:31.533+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:31.535+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:02:31.542+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:02:31.543+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:02:34.558+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:02:34.576+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:02:34.577+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:02:52.850+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:52.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:02:54.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:55.187+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 324 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:55.652+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:02:56.193+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.198+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.205+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.211+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.215+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.720+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3863 ms
2025-07-15T11:02:56.989+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:57.294+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@56a012e1
2025-07-15T11:02:57.298+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:57.309+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:02:57.649+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:57.717+05:30  INFO 16564 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:57.770+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:58.132+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:03:00.450+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:03:01.107+05:30  INFO 16564 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:03:01.461+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:03:02.615+05:30  WARN 16564 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:03:02.703+05:30  WARN 16564 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:03:02.779+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:03.334+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:05.449+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:03:06.606+05:30  INFO 16564 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:03:06.669+05:30  WARN 16564 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:03:06.757+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:03:06.783+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:03:06.787+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:03:06.796+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:03:06.799+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:03:07.093+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:03:07.095+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:03:07.097+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:03:07.099+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557587098 with initial instances count: 5
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557587110, current=UP, previous=STARTING]
2025-07-15T11:03:07.111+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:03:07.112+05:30  WARN 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:03:07.131+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:03:07.133+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 14.98 seconds (process running for 15.824)
2025-07-15T11:05:00.021+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:05:00.021928800
2025-07-15T11:05:00.047+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:05:00.047475300 to 2023-01-15T11:05:00.047475300
2025-07-15T11:05:01.630+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:05:01.644+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:08:06.809+05:30  INFO 16564 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:08:39.171+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:08:39.173+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:08:39.177+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-15T11:08:39.310+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.311+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.573+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:09:35.547+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:09:35.613+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:35.643+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-20250715-33D049 has been updated successfully
2025-07-15T11:09:43.559+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:43.572+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:43.580+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:43.607+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:46.164+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:46.165+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:46.242+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:46.243+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:46.254+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:46.255+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-20250715-08BF5C
2025-07-15T11:09:46.271+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:09:57.930+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:57.949+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:57.950+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:57.962+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:58.476+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:58.478+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:58.483+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:58.484+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:58.485+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:58.486+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-20250715-3184AE
2025-07-15T11:09:58.489+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:10:00.007086300
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:10:00.007086300 to 2023-01-15T11:10:00.007086300
2025-07-15T11:10:00.017+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:10:00.018+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:10:52.959+05:30  INFO 16564 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:10:52.971+05:30  INFO 16564 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:10:52.977+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052977, current=DOWN, previous=UP]
2025-07-15T11:10:52.991+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052991, current=UP, previous=DOWN]
2025-07-15T11:10:53.045+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:10:53.220+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:10:53.477+05:30  INFO 16564 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:10:53.569+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:10:53.670+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:10:53.685+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:10:56.702+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:10:56.731+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:10:56.735+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:10:57.403+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:10:58.057+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:10:58.061+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:11:00.903+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:11:01.158+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 254 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:23.325+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 12912 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:16:23.328+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:16:26.723+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:16:27.267+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 529 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:28.076+05:30  INFO 12912 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:16:29.350+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.362+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.379+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.385+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.393+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.976+05:30  INFO 12912 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:16:30.162+05:30  INFO 12912 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:16:30.241+05:30  INFO 12912 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:16:30.756+05:30  INFO 12912 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:16:30.804+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:16:31.092+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@1fa24e7
2025-07-15T11:16:31.094+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:16:34.735+05:30  INFO 12912 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:16:35.365+05:30  INFO 12912 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:35.925+05:30  INFO 12912 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:16:38.012+05:30  WARN 12912 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:16:38.413+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:39.506+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:42.676+05:30  INFO 12912 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:16:44.109+05:30  INFO 12912 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:16:44.177+05:30  WARN 12912 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:16:44.289+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:16:44.335+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:16:44.344+05:30  INFO 12912 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:16:44.838+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:16:44.840+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:16:44.842+05:30  INFO 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:16:44.844+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558404844 with initial instances count: 5
2025-07-15T11:16:44.854+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:16:44.855+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558404855, current=UP, previous=STARTING]
2025-07-15T11:16:44.857+05:30  WARN 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:44.905+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:16:44.910+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 22.626 seconds (process running for 24.666)
2025-07-15T11:16:44.976+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:16:46.134+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:16:46.135+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558406135, current=DOWN, previous=UP]
2025-07-15T11:16:46.136+05:30  WARN 12912 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:46.208+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:46.215+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:16:46.232+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:16:46.234+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:16:49.241+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:16:49.261+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:16:49.262+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:17:24.444+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:17:24.453+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:17:24.913+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:17:24.915+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:17:32.056+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:17:33.268+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1181 ms. Found 31 JPA repository interfaces.
2025-07-15T11:17:34.945+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:17:37.045+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.060+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.082+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.097+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.117+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:39.332+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:17:39.390+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:17:39.393+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:17:39.715+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:17:39.719+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 14797 ms
2025-07-15T11:17:40.545+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:17:41.316+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@78195ee2
2025-07-15T11:17:41.329+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:17:41.381+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:17:42.493+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:17:42.637+05:30  INFO 26816 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:17:42.707+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:17:43.199+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:17:45.871+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:17:46.456+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:17:46.866+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:17:48.196+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:17:48.294+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:17:48.373+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:49.054+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:51.385+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:17:52.530+05:30  INFO 26816 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:17:52.592+05:30  WARN 26816 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:17:52.685+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:17:52.725+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:17:52.731+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:17:52.740+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:17:52.742+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:17:52.744+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:17:53.086+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:17:53.088+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:17:53.090+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:17:53.092+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558473091 with initial instances count: 5
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558473106, current=UP, previous=STARTING]
2025-07-15T11:17:53.107+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:17:53.107+05:30  WARN 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:17:53.125+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:17:53.127+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:17:53.163+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 31.066 seconds (process running for 33.766)
2025-07-15T11:17:53.164+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:18:57.646+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:18:57.647+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:18:57.652+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:58.054+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:19:59.623+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:19:59.716+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:19:59.755+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-20250715-6B6267 has been updated successfully
2025-07-15T11:20:00.018+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:20:00.*********
2025-07-15T11:20:00.022+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:20:00.********* to 2023-01-15T11:20:00.*********
2025-07-15T11:20:00.146+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:20:00.147+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:20:07.819+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:20:07.839+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:20:07.852+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 0c70e3cf-2a5b-48b1-8065-96e0892eb972 has been updated successfully
2025-07-15T11:20:07.881+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:20:09.575+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:20:09.577+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:20:09.734+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:20:09.735+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:20:09.750+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:20:09.753+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : No agent available for auto-assignment to NCBSC application REF-20250715-D55F0F
2025-07-15T11:22:11.739+05:30  INFO 26816 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 10 class path changes (0 additions, 0 deletions, 10 modifications)
2025-07-15T11:22:11.749+05:30  INFO 26816 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:22:11.765+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731765, current=DOWN, previous=UP]
2025-07-15T11:22:11.780+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731780, current=UP, previous=DOWN]
2025-07-15T11:22:11.784+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:22:11.816+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:22:11.856+05:30  INFO 26816 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:11.866+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:22:11.885+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:22:11.893+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:22:14.897+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:22:14.903+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:22:14.904+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:22:15.060+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:22:15.178+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:22:15.179+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:22:16.355+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:22:16.578+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 221 ms. Found 31 JPA repository interfaces.
2025-07-15T11:22:16.843+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:22:16.980+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.985+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.990+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.994+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.997+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:17.256+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:22:17.337+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:22:17.338+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2154 ms
2025-07-15T11:22:17.513+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:22:17.604+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@68415437
2025-07-15T11:22:17.605+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:22:17.606+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:22:17.902+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:22:17.908+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:22:17.932+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:22:19.490+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:22:20.271+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:21.170+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:22:21.310+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:22:21.409+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:22:22.034+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
