2025-07-15T11:02:04.843+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 14936 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:04.860+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:02:09.639+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:10.929+05:30  INFO 14936 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1269 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:12.124+05:30  INFO 14936 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:02:13.863+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.873+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.882+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:13.887+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:13.892+05:30  WARN 14936 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:14.464+05:30  INFO 14936 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:14.682+05:30  INFO 14936 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:14.788+05:30  INFO 14936 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:15.643+05:30  INFO 14936 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:02:15.715+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:16.155+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@47cf65f1
2025-07-15T11:02:16.158+05:30  INFO 14936 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:20.228+05:30  INFO 14936 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:02:21.046+05:30  INFO 14936 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:21.710+05:30  INFO 14936 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:02:23.862+05:30  WARN 14936 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:02:24.172+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:25.245+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:02:28.453+05:30  INFO 14936 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:02:29.768+05:30  INFO 14936 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:02:29.840+05:30  WARN 14936 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:02:29.979+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:02:30.028+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:02:30.038+05:30  INFO 14936 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:02:30.056+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:02:30.057+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:02:30.511+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:02:30.515+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:02:30.518+05:30  INFO 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:02:30.521+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557550519 with initial instances count: 5
2025-07-15T11:02:30.528+05:30  INFO 14936 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:02:30.529+05:30  INFO 14936 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557550529, current=UP, previous=STARTING]
2025-07-15T11:02:30.530+05:30  WARN 14936 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:30.577+05:30  INFO 14936 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 26.29 seconds (process running for 29.607)
2025-07-15T11:02:30.580+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:02:30.658+05:30  INFO 14936 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:02:31.492+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557551492, current=DOWN, previous=UP]
2025-07-15T11:02:31.494+05:30  WARN 14936 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:02:31.533+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:02:31.535+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:02:31.542+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:02:31.543+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:02:34.558+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:02:34.576+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:02:34.577+05:30  INFO 14936 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:02:52.850+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:02:52.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:02:52.948+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:02:54.853+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:02:55.187+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 324 ms. Found 31 JPA repository interfaces.
2025-07-15T11:02:55.652+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:02:56.193+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.198+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.205+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:02:56.211+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.215+05:30  WARN 16564 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:02:56.720+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:02:56.735+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:02:56.812+05:30  INFO 16564 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3863 ms
2025-07-15T11:02:56.989+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:02:57.294+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@56a012e1
2025-07-15T11:02:57.298+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:02:57.309+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:02:57.649+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:02:57.717+05:30  INFO 16564 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:02:57.770+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:02:58.132+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:03:00.450+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:03:01.107+05:30  INFO 16564 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:03:01.461+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:03:02.615+05:30  WARN 16564 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:03:02.703+05:30  WARN 16564 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:03:02.779+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:03.334+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:03:05.449+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:03:06.606+05:30  INFO 16564 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:03:06.669+05:30  WARN 16564 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:03:06.757+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:03:06.783+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:03:06.787+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:03:06.796+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:03:06.797+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:03:06.798+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:03:06.799+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:03:07.093+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:03:07.095+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:03:07.097+05:30  INFO 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:03:07.099+05:30  INFO 16564 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752557587098 with initial instances count: 5
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:03:07.110+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752557587110, current=UP, previous=STARTING]
2025-07-15T11:03:07.111+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:03:07.112+05:30  WARN 16564 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:03:07.131+05:30  INFO 16564 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:03:07.133+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:03:07.173+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 14.98 seconds (process running for 15.824)
2025-07-15T11:05:00.021+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:05:00.021928800
2025-07-15T11:05:00.047+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:05:00.047475300 to 2023-01-15T11:05:00.047475300
2025-07-15T11:05:01.630+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:05:01.644+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:08:06.809+05:30  INFO 16564 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:08:39.171+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:08:39.173+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:08:39.177+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-15T11:08:39.310+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.311+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:08:39.573+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:09:35.547+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:09:35.613+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:35.643+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-33D049 has been updated successfully
2025-07-15T11:09:43.559+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:43.572+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:43.580+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:43.607+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:46.164+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:46.165+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:46.242+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:46.243+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:46.254+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:46.255+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-********-08BF5C
2025-07-15T11:09:46.271+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-3] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:09:57.930+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:09:57.949+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:09:57.950+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 8f61a8d3-49c7-44f0-986a-5fc529e2daa6 has been updated successfully
2025-07-15T11:09:57.962+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:09:58.476+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:09:58.478+05:30  INFO 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:09:58.483+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:09:58.484+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:09:58.485+05:30 ERROR 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:09:58.486+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : No agent available for auto-assignment to NCBSC application REF-********-3184AE
2025-07-15T11:09:58.489+05:30  WARN 16564 --- [workplace-learning] [http-nio-8091-exec-4] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only]
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:10:00.007086300
2025-07-15T11:10:00.007+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:10:00.007086300 to 2023-01-15T11:10:00.007086300
2025-07-15T11:10:00.017+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:10:00.018+05:30  INFO 16564 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:10:52.959+05:30  INFO 16564 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:10:52.971+05:30  INFO 16564 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:10:52.977+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052977, current=DOWN, previous=UP]
2025-07-15T11:10:52.991+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558052991, current=UP, previous=DOWN]
2025-07-15T11:10:53.045+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:10:53.220+05:30  INFO 16564 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:10:53.477+05:30  INFO 16564 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:10:53.569+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:10:53.670+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:10:53.685+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:10:56.702+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:10:56.731+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:10:56.735+05:30  INFO 16564 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:10:57.403+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:10:58.057+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 16564 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:10:58.061+05:30  INFO 16564 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:11:00.903+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:11:01.158+05:30  INFO 16564 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 254 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:23.325+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Starting WorkplaceLearningApplicationTests using Java 21.0.6 with PID 12912 (started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:16:23.328+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : The following 1 profile is active: "dev"
2025-07-15T11:16:26.723+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:16:27.267+05:30  INFO 12912 --- [workplace-learning] [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 529 ms. Found 31 JPA repository interfaces.
2025-07-15T11:16:28.076+05:30  INFO 12912 --- [workplace-learning] [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=79da4217-4a96-3d02-9797-f31ff5c7a582
2025-07-15T11:16:29.350+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.362+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.379+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:16:29.385+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.393+05:30  WARN 12912 --- [workplace-learning] [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:16:29.976+05:30  INFO 12912 --- [workplace-learning] [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:16:30.162+05:30  INFO 12912 --- [workplace-learning] [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:16:30.241+05:30  INFO 12912 --- [workplace-learning] [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:16:30.756+05:30  INFO 12912 --- [workplace-learning] [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:16:30.804+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:16:31.092+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@1fa24e7
2025-07-15T11:16:31.094+05:30  INFO 12912 --- [workplace-learning] [main] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:16:34.735+05:30  INFO 12912 --- [workplace-learning] [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:16:35.365+05:30  INFO 12912 --- [workplace-learning] [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:35.925+05:30  INFO 12912 --- [workplace-learning] [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:16:38.012+05:30  WARN 12912 --- [workplace-learning] [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:16:38.413+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:39.506+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:16:42.676+05:30  INFO 12912 --- [workplace-learning] [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:16:44.109+05:30  INFO 12912 --- [workplace-learning] [main] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:16:44.177+05:30  WARN 12912 --- [workplace-learning] [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:16:44.289+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:16:44.335+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:16:44.344+05:30  INFO 12912 --- [workplace-learning] [main] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:16:44.364+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:16:44.365+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:16:44.838+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:16:44.840+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:16:44.842+05:30  INFO 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:16:44.844+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558404844 with initial instances count: 5
2025-07-15T11:16:44.854+05:30  INFO 12912 --- [workplace-learning] [main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:16:44.855+05:30  INFO 12912 --- [workplace-learning] [main] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558404855, current=UP, previous=STARTING]
2025-07-15T11:16:44.857+05:30  WARN 12912 --- [workplace-learning] [main] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:44.905+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:16:44.910+05:30  INFO 12912 --- [workplace-learning] [main] .h.w.w.WorkplaceLearningApplicationTests : Started WorkplaceLearningApplicationTests in 22.626 seconds (process running for 24.666)
2025-07-15T11:16:44.976+05:30  INFO 12912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:16:46.134+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:16:46.135+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558406135, current=DOWN, previous=UP]
2025-07-15T11:16:46.136+05:30  WARN 12912 --- [workplace-learning] [SpringApplicationShutdownHook] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:16:46.208+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:16:46.215+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:16:46.232+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:16:46.234+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:16:49.241+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:16:49.261+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:16:49.262+05:30  INFO 12912 --- [workplace-learning] [SpringApplicationShutdownHook] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:17:24.444+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:17:24.453+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:17:24.913+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:17:24.915+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:17:32.056+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:17:33.268+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1181 ms. Found 31 JPA repository interfaces.
2025-07-15T11:17:34.945+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:17:37.045+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.060+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.082+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:17:37.097+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:37.117+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:17:39.332+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:17:39.390+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:17:39.393+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:17:39.715+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:17:39.719+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 14797 ms
2025-07-15T11:17:40.545+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:17:41.316+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@78195ee2
2025-07-15T11:17:41.329+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:17:41.381+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:17:42.493+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:17:42.637+05:30  INFO 26816 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:17:42.707+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:17:43.199+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:17:45.871+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:17:46.456+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:17:46.866+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:17:48.196+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:17:48.294+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:17:48.373+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:49.054+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:17:51.385+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:17:52.530+05:30  INFO 26816 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:17:52.592+05:30  WARN 26816 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:17:52.685+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:17:52.725+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:17:52.731+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:17:52.740+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:17:52.741+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:17:52.742+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:17:52.743+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:17:52.744+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:17:53.086+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:17:53.088+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:17:53.090+05:30  INFO 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:17:53.092+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752558473091 with initial instances count: 5
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:17:53.106+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558473106, current=UP, previous=STARTING]
2025-07-15T11:17:53.107+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:17:53.107+05:30  WARN 26816 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:17:53.125+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:17:53.127+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:17:53.163+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 31.066 seconds (process running for 33.766)
2025-07-15T11:17:53.164+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:18:57.646+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:18:57.647+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:18:57.652+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:57.773+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:18:58.054+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:19:59.623+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:19:59.716+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:19:59.755+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-6B6267 has been updated successfully
2025-07-15T11:20:00.018+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:20:00.*********
2025-07-15T11:20:00.022+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:20:00.********* to 2023-01-15T11:20:00.*********
2025-07-15T11:20:00.146+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:20:00.147+05:30  INFO 26816 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:20:07.819+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:20:07.839+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:20:07.852+05:30  INFO 26816 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 0c70e3cf-2a5b-48b1-8065-96e0892eb972 has been updated successfully
2025-07-15T11:20:07.881+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:20:09.575+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:20:09.577+05:30  INFO 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:20:09.734+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 42703
2025-07-15T11:20:09.735+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158
2025-07-15T11:20:09.750+05:30 ERROR 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch agents for assignment: JDBC exception executing SQL [SELECT assigned_agent, COUNT(*) AS application_count
FROM ncbsc_application
WHERE application_state = 'IN_PROCESSING'
AND application_status = 'PENDING'
AND is_deleted = false
AND assigned_agent IS NOT NULL
GROUP BY assigned_agent
ORDER BY application_count ASC
] [ERROR: column "is_deleted" does not exist
  Hint: Perhaps you meant to reference the column "ncbsc_application.deleted".
  Position: 158] [n/a]; SQL [n/a]
2025-07-15T11:20:09.753+05:30  WARN 26816 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : No agent available for auto-assignment to NCBSC application REF-********-D55F0F
2025-07-15T11:22:11.739+05:30  INFO 26816 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 10 class path changes (0 additions, 0 deletions, 10 modifications)
2025-07-15T11:22:11.749+05:30  INFO 26816 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:22:11.765+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731765, current=DOWN, previous=UP]
2025-07-15T11:22:11.780+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752558731780, current=UP, previous=DOWN]
2025-07-15T11:22:11.784+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:22:11.816+05:30  INFO 26816 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:22:11.856+05:30  INFO 26816 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:11.866+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:22:11.885+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:22:11.893+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:22:14.897+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:22:14.903+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:22:14.904+05:30  INFO 26816 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:22:15.060+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:22:15.178+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 26816 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:22:15.179+05:30  INFO 26816 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:22:16.355+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:22:16.578+05:30  INFO 26816 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 221 ms. Found 31 JPA repository interfaces.
2025-07-15T11:22:16.843+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:22:16.980+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.985+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.990+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:22:16.994+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:16.997+05:30  WARN 26816 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:22:17.256+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:22:17.260+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:22:17.337+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:22:17.338+05:30  INFO 26816 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2154 ms
2025-07-15T11:22:17.513+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:22:17.604+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@68415437
2025-07-15T11:22:17.605+05:30  INFO 26816 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:22:17.606+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:22:17.902+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:22:17.908+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:22:17.932+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:22:19.490+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:22:20.271+05:30  INFO 26816 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:22:21.170+05:30  WARN 26816 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:22:21.310+05:30  WARN 26816 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:22:21.409+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:22:22.034+05:30  INFO 26816 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:19.416+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:27:19.426+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:27:19.759+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T11:27:19.761+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T11:27:28.973+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:27:30.404+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1393 ms. Found 31 JPA repository interfaces.
2025-07-15T11:27:33.129+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:27:35.520+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:27:35.553+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:35.588+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:27:35.612+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:35.628+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:27:38.752+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:27:38.837+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:27:38.842+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:27:39.306+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:27:39.309+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 19544 ms
2025-07-15T11:27:40.180+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:27:41.104+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@2b3bea85
2025-07-15T11:27:41.116+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:27:41.181+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:27:42.940+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:27:43.233+05:30  INFO 21912 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T11:27:43.502+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:27:45.080+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:27:50.722+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:27:51.568+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:27:52.261+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T11:27:53.797+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:27:53.896+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:27:53.972+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:54.715+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:27:57.422+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:27:58.700+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:27:58.775+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:27:58.873+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:27:58.900+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:27:58.904+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:27:58.913+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:27:58.914+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:27:58.914+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:27:58.915+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:27:58.916+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:27:59.249+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:27:59.251+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:27:59.253+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:27:59.255+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559079254 with initial instances count: 5
2025-07-15T11:27:59.267+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559079267, current=UP, previous=STARTING]
2025-07-15T11:27:59.268+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:27:59.273+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:27:59.274+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:27:59.294+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:27:59.295+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:27:59.334+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:27:59.342+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 42.061 seconds (process running for 42.736)
2025-07-15T11:29:12.408+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T11:29:12.409+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T11:29:12.412+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-15T11:29:12.534+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:29:12.535+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T11:29:12.785+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T11:30:00.010+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T11:30:00.*********
2025-07-15T11:30:00.011+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T11:30:00.********* to 2023-01-15T11:30:00.*********
2025-07-15T11:30:00.084+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T11:30:00.084+05:30  INFO 21912 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T11:30:03.304+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T11:30:03.358+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:30:03.386+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-F68C64 has been updated successfully
2025-07-15T11:30:09.168+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T11:30:09.176+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T11:30:09.183+05:30  INFO 21912 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number f240c561-3d0c-4648-bd91-5d37b73bc8c0 has been updated successfully
2025-07-15T11:30:09.202+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T11:30:10.782+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T11:30:10.783+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T11:30:10.868+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T11:30:10.869+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 0 active complaints
2025-07-15T11:30:10.870+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-15T11:30:10.893+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Auto-assigned agent ad9f2ba3-898c-487f-a40e-893a10fe8477 to NCBSC application REF-********-70EAF4
2025-07-15T11:30:13.389+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Workflow response: {applicationData={courseDeliverySchedule=[{date=2024-01-15T00:00:00.000+00:00, topic=Introduction to Python, hours=4, Id=2397373010388100, Uuid=ca029ec4-f186-4968-829c-c871a325f606}, {date=2024-01-16T00:00:00.000+00:00, topic=Data Types and Variables, hours=3, Id=2397373010730500, Uuid=06f57b2a-628a-4a6f-aa55-0aad5b4b3dcb}], scopeOfAccreditations=[{fieldsOfLearningAccredited=Project Management, Id=2397373003125600, Uuid=043b81bb-421a-4822-96d4-298b5f171917}, {fieldsOfLearningAccredited=Information Technology, Id=2397373003653800, Uuid=7aba0d9b-2fec-460a-87fb-1f2e3f74e720}], attachments=[], application={assignedAgent=ad9f2ba3-898c-487f-a40e-893a10fe8477, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, referenceNumber=REF-********-70EAF4, applicationNumber=APP-********-04DC13, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, trainingNeedsAssessmentPurpose=Identify skill gaps in IT, trainingNeedsAssessmentSkillsNeedsAnalysis=Survey-based analysis of existing workforce skills, shortCourseDeliveryMode=Online, keyFacilitation=Experienced trainers with relevant certifications, assessmentType=Multiple-choice tests and project evaluations, certification=Certificate of completion, thirdPartyArrangements=Collaboration with ABC Institute, resources=Resources needed for the course, shortCourseEndorsement=Course Endorsement, dateSubmitted=2025-07-15T11:29:12.658721, applicationStatus=PENDING, applicationState=IN_PROCESSING, shortCourseInformation={title=Introduction to Sewing, type=Workshop, duration=2 weeks, fieldOfLearning=Computer Science, subFieldOfLearning=Conditional operations, level=NFQ, accreditingBody=HRDC, courseLearningTime=40, startDate=null, endDate=null, yearDueForReview=2025, targetPopulation=University students, entryRequirements=Basic computer literacy, Id=2397373005652300, Uuid=e3371293-6cb1-492a-8c98-c4ac9f0bee31}, courseContentAndDelivery={exitLevelOutcomes=Write basic programs in Python, learningOutcomesSummary=, shortCourseDeliveryMode=, shortCourseDeliveryType=, location=, Id=2397373006807500, Uuid=7cfb1366-c0fb-4f6c-ab89-30a621787046}, processInstanceId=null, managerApprovedAt=null, renewalNotificationSent=false, renewalNotificationSentAt=null, Id=2397372984629400, Uuid=f240c561-3d0c-4648-bd91-5d37b73bc8c0, CreatedAt=2025-07-15T11:29:12.734894, UpdatedAt=2025-07-15T11:30:10.888909, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}, documents=[{key=documents/pop/payment-proof-REF-20250710-A8A5F9.pdf, docName=null, docExt=null, identifier=f240c561-3d0c-4648-bd91-5d37b73bc8c0, fileType=null, docSize=null, fileUrl=null, Id=2397429471452400, Uuid=70c134fc-9161-4b69-9753-fb98c84fd8e8, CreatedAt=2025-07-15T11:30:09.190676, UpdatedAt=2025-07-15T11:30:09.190676, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}], learningOutcomes=[{id=2397373007738000, assessmentCriteria=[{topic=, objective=, deliveryStrategy=, assessmentStrategy=, dateFrom=null, dateTo=null, hours=null, Id=2397373009423600, Uuid=d8839da7-f74c-42a3-8e6f-20d7f33a89e6}], uuid=145c3158-d838-4e3a-95ff-a31d22c1faed, outcome=Understand Python syntax}], quotation={quoteRef=QUO-********-E9A733, reference=f240c561-3d0c-4648-bd91-5d37b73bc8c0, checklistItemsJson=null, totalAmount=5000.0, accepted=true, Id=2397373081663000, Uuid=df95b2f2-f2c1-4d7e-8994-ebc5a2882f9f, CreatedAt=2025-07-15T11:29:12.795066, UpdatedAt=2025-07-15T11:30:03.419967, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}}, success=true, message=Process started and application data retrieved successfully, processInstanceId=f7dbe565-6140-11f0-ae19-00155d4133f2}
2025-07-15T11:30:13.401+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Successfully updated process instance ID f7dbe565-6140-11f0-ae19-00155d4133f2 for application REF-********-70EAF4
2025-07-15T11:30:13.402+05:30  INFO 21912 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Process instance ID f7dbe565-6140-11f0-ae19-00155d4133f2 saved for application REF-********-70EAF4
2025-07-15T11:32:58.922+05:30  INFO 21912 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:34:53.224+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:34:53.227+05:30  INFO 21912 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:34:53.227+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559493227, current=DOWN, previous=UP]
2025-07-15T11:34:53.230+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559493230, current=UP, previous=DOWN]
2025-07-15T11:34:53.230+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:34:53.235+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:34:53.256+05:30  INFO 21912 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:34:53.259+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:34:53.265+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:34:53.267+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:34:56.271+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:34:56.289+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:34:56.290+05:30  INFO 21912 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:34:56.405+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:34:56.521+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:34:56.522+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:35:01.144+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:35:02.260+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1112 ms. Found 31 JPA repository interfaces.
2025-07-15T11:35:03.558+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:35:04.258+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:04.327+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:04.442+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:04.476+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:04.489+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:05.947+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:35:05.981+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:35:06.013+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:35:06.510+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:35:06.544+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 10015 ms
2025-07-15T11:35:07.838+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:35:08.502+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6dc18b47
2025-07-15T11:35:08.553+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:35:08.577+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:35:11.034+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:35:11.077+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:35:11.253+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:35:18.855+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:35:22.480+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:26.653+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:35:27.036+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:35:27.308+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:30.060+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:34.744+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:35:36.635+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:35:36.710+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:35:36.841+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:35:36.854+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:35:36.855+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:35:36.856+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:35:36.857+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:35:36.858+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:35:36.858+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:35:36.859+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:35:36.860+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:35:36.860+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:35:36.978+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:35:36.979+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:35:36.979+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:35:36.980+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559536980 with initial instances count: 5
2025-07-15T11:35:36.984+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559536984, current=UP, previous=STARTING]
2025-07-15T11:35:36.984+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:35:37.000+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:35:37.006+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:35:37.009+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:35:37.012+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:35:37.015+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:35:37.052+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 40.633 seconds (process running for 500.447)
2025-07-15T11:35:37.058+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-15T11:35:38.386+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:35:38.391+05:30  INFO 21912 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:35:38.393+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559538393, current=DOWN, previous=UP]
2025-07-15T11:35:38.394+05:30  WARN 21912 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:35:38.455+05:30  INFO 21912 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:38.461+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:35:38.472+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:35:38.481+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T11:35:41.489+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T11:35:41.563+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T11:35:41.564+05:30  INFO 21912 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T11:35:41.687+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T11:35:41.746+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 21912 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T11:35:41.747+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T11:35:42.345+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T11:35:42.487+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 141 ms. Found 31 JPA repository interfaces.
2025-07-15T11:35:42.637+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T11:35:42.722+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:42.724+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:42.728+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T11:35:42.731+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:42.732+05:30  WARN 21912 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T11:35:43.216+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T11:35:43.217+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T11:35:43.217+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T11:35:43.258+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T11:35:43.259+05:30  INFO 21912 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1509 ms
2025-07-15T11:35:43.364+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T11:35:43.433+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@773e8c6f
2025-07-15T11:35:43.434+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T11:35:43.434+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T11:35:43.588+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T11:35:43.593+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T11:35:43.613+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T11:35:48.046+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T11:35:53.646+05:30  INFO 21912 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:35:56.036+05:30  WARN 21912 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T11:35:56.376+05:30  WARN 21912 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T11:35:56.873+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:35:58.663+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T11:36:05.570+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T11:36:09.318+05:30  INFO 21912 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T11:36:09.563+05:30  WARN 21912 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T11:36:09.760+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T11:36:09.794+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T11:36:09.829+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T11:36:09.856+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T11:36:09.904+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T11:36:09.989+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T11:36:10.048+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T11:36:10.071+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T11:36:10.093+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T11:36:10.106+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T11:36:10.342+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T11:36:10.372+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T11:36:10.379+05:30  INFO 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T11:36:10.397+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752559570397 with initial instances count: 5
2025-07-15T11:36:10.450+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T11:36:10.465+05:30  INFO 21912 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559570465, current=UP, previous=STARTING]
2025-07-15T11:36:10.497+05:30  WARN 21912 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:36:10.500+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T11:36:10.535+05:30  INFO 21912 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T11:36:10.676+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T11:36:10.705+05:30  INFO 21912 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T11:36:10.836+05:30  INFO 21912 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 29.143 seconds (process running for 534.23)
2025-07-15T11:36:10.906+05:30  INFO 21912 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-15T11:36:11.069+05:30  INFO 21912 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T11:36:11.157+05:30  INFO 21912 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T11:36:11.201+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752559571201, current=DOWN, previous=UP]
2025-07-15T11:36:11.237+05:30  WARN 21912 --- [workplace-learning] [Thread-13] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T11:36:11.299+05:30  INFO 21912 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T11:36:11.343+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T11:36:11.420+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T11:36:11.457+05:30  INFO 21912 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T12:27:30.686+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 13672 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T12:27:30.688+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T12:27:30.777+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-15T12:27:30.777+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-15T12:27:32.746+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T12:27:33.098+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 343 ms. Found 31 JPA repository interfaces.
2025-07-15T12:27:33.567+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T12:27:34.143+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T12:27:34.150+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T12:27:34.157+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T12:27:34.161+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T12:27:34.164+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T12:27:34.838+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T12:27:34.857+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T12:27:34.858+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T12:27:34.951+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T12:27:34.952+05:30  INFO 13672 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4173 ms
2025-07-15T12:27:35.142+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T12:27:35.338+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@437dab2f
2025-07-15T12:27:35.341+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T12:27:35.353+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T12:27:35.751+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T12:27:35.865+05:30  INFO 13672 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-15T12:27:35.922+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T12:27:36.360+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T12:27:39.149+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T12:27:39.765+05:30  INFO 13672 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T12:27:40.205+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15T12:27:46.749+05:30  WARN 13672 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T12:27:47.366+05:30  WARN 13672 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T12:27:47.756+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T12:27:50.846+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T12:27:59.975+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T12:28:01.559+05:30  INFO 13672 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T12:28:01.698+05:30  WARN 13672 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T12:28:01.913+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T12:28:02.005+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T12:28:02.059+05:30  INFO 13672 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:28:02.093+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T12:28:02.115+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T12:28:02.117+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T12:28:02.117+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T12:28:02.119+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T12:28:02.121+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T12:28:02.122+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T12:28:02.780+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T12:28:02.783+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T12:28:02.787+05:30  INFO 13672 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T12:28:02.790+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752562682789 with initial instances count: 5
2025-07-15T12:28:02.810+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752562682810, current=UP, previous=STARTING]
2025-07-15T12:28:02.811+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T12:28:02.812+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T12:28:02.814+05:30  WARN 13672 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T12:28:02.840+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T12:28:02.842+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T12:28:02.913+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T12:28:02.920+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 32.917 seconds (process running for 33.654)
2025-07-15T12:29:37.402+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15T12:29:37.404+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-15T12:29:37.408+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-07-15T12:29:37.517+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T12:29:37.517+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T12:29:37.769+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T12:30:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:30:00.*********
2025-07-15T12:30:00.015+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:30:00.********* to 2023-01-15T12:30:00.*********
2025-07-15T12:30:00.103+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:30:00.103+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:33:02.137+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:33:33.324+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T12:33:33.368+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T12:33:33.395+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-9DDF61 has been updated successfully
2025-07-15T12:33:42.314+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T12:33:42.343+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T12:33:42.400+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number a9a88ec1-b14e-4122-a135-0deefc492f19 has been updated successfully
2025-07-15T12:33:42.444+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T12:33:43.879+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T12:33:43.881+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T12:33:43.986+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T12:33:43.987+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 0 active complaints
2025-07-15T12:33:43.987+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-15T12:33:44.007+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Auto-assigned agent ad9f2ba3-898c-487f-a40e-893a10fe8477 to NCBSC application REF-********-3CF2F7
2025-07-15T12:33:49.345+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Workflow response: {applicationData={courseDeliverySchedule=[{date=2024-01-15T00:00:00.000+00:00, topic=Introduction to Python, hours=4, Id=2400996488635000, Uuid=e45a5804-7d0f-4b80-8aeb-fde904f3a6d4}, {date=2024-01-16T00:00:00.000+00:00, topic=Data Types and Variables, hours=3, Id=2400996488830800, Uuid=05a2728c-1397-4d53-9e5f-f9acbc430817}], scopeOfAccreditations=[{fieldsOfLearningAccredited=Project Management, Id=2400996482059800, Uuid=d2fe75a5-b662-49bf-86be-e8b6dcc90101}, {fieldsOfLearningAccredited=Information Technology, Id=2400996482660700, Uuid=b5100ebf-1562-4625-bd64-636029e29e48}], attachments=[], application={assignedAgent=ad9f2ba3-898c-487f-a40e-893a10fe8477, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, referenceNumber=REF-********-3CF2F7, applicationNumber=APP-********-20EC90, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, trainingNeedsAssessmentPurpose=Identify skill gaps in IT, trainingNeedsAssessmentSkillsNeedsAnalysis=Survey-based analysis of existing workforce skills, shortCourseDeliveryMode=Online, keyFacilitation=Experienced trainers with relevant certifications, assessmentType=Multiple-choice tests and project evaluations, certification=Certificate of completion, thirdPartyArrangements=Collaboration with ABC Institute, resources=Resources needed for the course, shortCourseEndorsement=Course Endorsement, dateSubmitted=2025-07-15T12:29:37.645107, applicationStatus=PENDING, applicationState=IN_PROCESSING, shortCourseInformation={title=Introduction to Sewing, type=Workshop, duration=2 weeks, fieldOfLearning=Computer Science, subFieldOfLearning=Conditional operations, level=NFQ, accreditingBody=HRDC, courseLearningTime=40, startDate=null, endDate=null, yearDueForReview=2025, targetPopulation=University students, entryRequirements=Basic computer literacy, Id=2400996484508800, Uuid=1079e6fe-2047-452b-aa7d-7cf02106a0ec}, courseContentAndDelivery={exitLevelOutcomes=Write basic programs in Python, learningOutcomesSummary=, shortCourseDeliveryMode=, shortCourseDeliveryType=, location=, Id=2400996485308300, Uuid=a4862750-c719-4968-9e00-ea1eb232ce7a}, processInstanceId=null, managerApprovedAt=null, renewalNotificationSent=false, renewalNotificationSentAt=null, deleted=false, Id=2400996461341600, Uuid=a9a88ec1-b14e-4122-a135-0deefc492f19, CreatedAt=2025-07-15T12:29:37.722663, UpdatedAt=2025-07-15T12:33:44.004766, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}, documents=[{key=documents/pop/payment-proof-REF-20250710-A8A5F9.pdf, docName=null, docExt=null, identifier=a9a88ec1-b14e-4122-a135-0deefc492f19, fileType=null, docSize=null, fileUrl=null, deleted=false, Id=2401241176709000, Uuid=38c60a10-bb9b-4e9a-9b74-aa561a157d3d, CreatedAt=2025-07-15T12:33:42.404999, UpdatedAt=2025-07-15T12:33:42.404999, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}], learningOutcomes=[{id=2400996485928200, assessmentCriteria=[{topic=, objective=, deliveryStrategy=, assessmentStrategy=, dateFrom=null, dateTo=null, hours=null, Id=2400996487571300, Uuid=076004a7-2172-4eb9-910f-5d152cc1f2ce}], uuid=d00d2444-80a2-40d6-9680-cdd34f74c660, outcome=Understand Python syntax}], quotation={quoteRef=QUO-********-EA12D4, reference=a9a88ec1-b14e-4122-a135-0deefc492f19, checklistItemsJson=null, totalAmount=5000.0, accepted=true, deleted=false, Id=2400996553268600, Uuid=5cbf9432-1094-4bdc-9330-2cc8608b3003, CreatedAt=2025-07-15T12:29:37.778147, UpdatedAt=2025-07-15T12:33:33.408827, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}}, processInstanceId=d899cbda-6149-11f0-aa8b-00155d4133f2, message=Process started and application data retrieved successfully, success=true}
2025-07-15T12:33:49.387+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Successfully updated process instance ID d899cbda-6149-11f0-aa8b-00155d4133f2 for application REF-********-3CF2F7
2025-07-15T12:33:49.405+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-1] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Process instance ID d899cbda-6149-11f0-aa8b-00155d4133f2 saved for application REF-********-3CF2F7
2025-07-15T12:35:00.015+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:35:00.015625
2025-07-15T12:35:00.018+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:35:00.017643100 to 2023-01-15T12:35:00.017643100
2025-07-15T12:35:00.037+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:35:00.038+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:38:02.142+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:39:30.695+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.NCBSCApplicationController   : Application user assignment initiated for application reference : REF-********-3CF2F7
2025-07-15T12:39:30.736+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-07-15T12:39:33.409+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.k.clients.producer.ProducerConfig    : Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-15T12:39:33.411+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.k.clients.producer.ProducerConfig    : ProducerConfig values: 
	acks = 1
	auto.include.jmx.reporter = true
	batch.size = 16384
	bootstrap.servers = [localhost:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = workplace-learning-producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	enable.metrics.push = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 10000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 3
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 1000
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.springframework.kafka.support.serializer.JsonSerializer

2025-07-15T12:39:33.485+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.k.c.t.i.KafkaMetricsCollector        : initializing Kafka metrics collector
2025-07-15T12:39:33.566+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.kafka.common.utils.AppInfoParser     : Kafka version: 3.7.1
2025-07-15T12:39:33.566+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.kafka.common.utils.AppInfoParser     : Kafka commitId: e2494e6ffb89f828
2025-07-15T12:39:33.567+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] o.a.kafka.common.utils.AppInfoParser     : Kafka startTimeMs: 1752563373564
2025-07-15T12:39:34.202+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.Metadata        : [Producer clientId=workplace-learning-producer-1] Cluster ID: -hYqUjHuTMe8MdzUw8S3LQ
2025-07-15T12:39:34.229+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-07-15T12:39:34.255+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[25]
2025-07-15T12:40:00.001+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:40:00.001357700
2025-07-15T12:40:00.003+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:40:00.003187700 to 2023-01-15T12:40:00.003187700
2025-07-15T12:40:00.023+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:40:00.024+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:40:42.043+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application status update initiated for application reference : REF-********-3CF2F7
2025-07-15T12:40:42.072+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.n.NCBSCApplicationService    : Auto-assigning agent for NCBSC application ID: a9a88ec1-b14e-4122-a135-0deefc492f19, role: OFFICER
2025-07-15T12:40:42.073+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T12:40:42.797+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=91c062fe-ae81-459b-bd16-c5cc21d408f8, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=89906ecf-0604-400e-b237-716db1d73cb6, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=7c0fe956-0f70-469a-85b3-2dc71befcb6a, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=a3b91e0e-803c-4231-86bd-f90b473503eb, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T12:40:42.799+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T12:40:42.828+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T12:40:42.829+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 91c062fe-ae81-459b-bd16-c5cc21d408f8 with 0 active complaints
2025-07-15T12:40:42.830+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 91c062fe-ae81-459b-bd16-c5cc21d408f8
2025-07-15T12:40:42.841+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.n.NCBSCApplicationService    : Assigned officer 91c062fe-ae81-459b-bd16-c5cc21d408f8 to NCBSC application ID: a9a88ec1-b14e-4122-a135-0deefc492f19
2025-07-15T12:40:42.843+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Initiated auto-assignment of OFFICER for application REF-********-3CF2F7
2025-07-15T12:40:42.857+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : processInstanceId : d899cbda-6149-11f0-aa8b-00155d4133f2
2025-07-15T12:40:42.858+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : User Role : AGENT and ref number :REF-********-3CF2F7
2025-07-15T12:43:02.153+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:44:22.390+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NCBSCApplicationController   : Application user assignment initiated for application reference : REF-********-3CF2F7
2025-07-15T12:44:22.415+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 91c062fe-ae81-459b-bd16-c5cc21d408f8
2025-07-15T12:44:24.745+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-07-15T12:44:24.752+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[34]
2025-07-15T12:44:50.322+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NCBSCApplicationController   : Application status update initiated for application reference : REF-********-3CF2F7
2025-07-15T12:44:50.345+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.n.NCBSCApplicationService    : Auto-assigning agent for NCBSC application ID: a9a88ec1-b14e-4122-a135-0deefc492f19, role: MANAGER
2025-07-15T12:44:50.345+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T12:44:51.036+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=8c6e2610-4a3b-4b58-ad49-283102f7d7ac, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=4012a23a-e0c6-494c-b78f-048177f1a0bf, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=1adb1243-b20d-4a2e-b02f-28d60cbf8795, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=7df89d26-811d-45c3-a694-e36af54ec7d6, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T12:44:51.037+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T12:44:51.053+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T12:44:51.053+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 8c6e2610-4a3b-4b58-ad49-283102f7d7ac with 0 active complaints
2025-07-15T12:44:51.054+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 8c6e2610-4a3b-4b58-ad49-283102f7d7ac
2025-07-15T12:44:51.061+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.n.NCBSCApplicationService    : Assigned manager 8c6e2610-4a3b-4b58-ad49-283102f7d7ac to NCBSC application ID: a9a88ec1-b14e-4122-a135-0deefc492f19
2025-07-15T12:44:51.063+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NCBSCApplicationController   : Initiated auto-assignment of MANAGER for application REF-********-3CF2F7
2025-07-15T12:44:51.068+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NCBSCApplicationController   : processInstanceId : d899cbda-6149-11f0-aa8b-00155d4133f2
2025-07-15T12:44:51.069+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NCBSCApplicationController   : User Role : OFFICER and ref number :REF-********-3CF2F7
2025-07-15T12:45:00.006+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:45:00.006105300
2025-07-15T12:45:00.007+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:45:00.007121800 to 2023-01-15T12:45:00.007121800
2025-07-15T12:45:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:45:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:48:02.164+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:48:34.271+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node -1 disconnected.
2025-07-15T12:50:00.014+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:50:00.014560600
2025-07-15T12:50:00.015+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:50:00.015566 to 2023-01-15T12:50:00.015566
2025-07-15T12:50:00.038+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:50:00.039+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:53:02.171+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:55:00.008+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T12:55:00.007263700
2025-07-15T12:55:00.009+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T12:55:00.009096200 to 2023-01-15T12:55:00.009096200
2025-07-15T12:55:00.028+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T12:55:00.028+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T12:58:02.186+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T12:58:34.261+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node -1 disconnected.
2025-07-15T13:00:00.001+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T13:00:00.001076100
2025-07-15T13:00:00.002+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T13:00:00.002075300 to 2023-01-15T13:00:00.002075300
2025-07-15T13:00:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T13:00:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T13:01:01.799+05:30  WARN 13672 --- [workplace-learning] [HikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Thread starvation or clock leap detected (housekeeper delta=55s908ms827µs400ns).
2025-07-15T13:03:28.059+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T13:03:36.210+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application status update initiated for application reference : REF-********-3CF2F7
2025-07-15T13:03:36.210+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : manager action
2025-07-15T13:03:36.245+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : processInstanceId : d899cbda-6149-11f0-aa8b-00155d4133f2
2025-07-15T13:03:36.245+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : User Role : MANAGER and ref number :REF-********-3CF2F7
2025-07-15T13:04:06.329+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application status update initiated for application reference : REF-********-3CF2F7
2025-07-15T13:04:06.329+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : manager action
2025-07-15T13:04:06.345+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : processInstanceId : d899cbda-6149-11f0-aa8b-00155d4133f2
2025-07-15T13:04:06.346+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : User Role : MANAGER and ref number :REF-********-3CF2F7
2025-07-15T13:04:06.373+05:30 ERROR 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Failed to resume workflow process: [404] during [POST] to [http://WORKFLOW/api/v1/workflow/resume-process/d899cbda-6149-11f0-aa8b-00155d4133f2/Manager_action] [WorkflowClient#resumeProcess(String,String,Map)]: [{
  "message" : "No execution found waiting at ManagerAction for process: d899cbda-6149-11f0-aa8b-00155d4133f2",
  "success" : false
}]
2025-07-15T13:05:25.873+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T13:05:25.873282200
2025-07-15T13:05:25.874+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T13:05:25.874251200 to 2023-01-15T13:05:25.874251200
2025-07-15T13:05:25.880+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T13:05:25.880+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:01:02.726+05:30  INFO 13672 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node 0 disconnected.
2025-07-15T15:01:08.715+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:01:12.612+05:30  WARN 13672 --- [workplace-learning] [HikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Thread starvation or clock leap detected (housekeeper delta=1h53m10s731ms108µs500ns).
2025-07-15T15:02:40.651+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:02:40.651637400
2025-07-15T15:02:40.652+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:02:40.652706500 to 2023-01-15T15:02:40.652706500
2025-07-15T15:02:40.666+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:02:40.667+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:05:00.010+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:05:00.010755400
2025-07-15T15:05:00.025+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:05:00.025755700 to 2023-01-15T15:05:00.025755700
2025-07-15T15:05:00.101+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:05:00.129+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:05:46.018+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:05:46.019+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250711-639CB6
2025-07-15T15:05:46.030+05:30 ERROR 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NOCApplicationController     : Application already exist for recognition application id REF-20250711-639CB6
2025-07-15T15:06:08.747+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:07:11.149+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:07:11.149+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250711-639CB6
2025-07-15T15:07:11.156+05:30 ERROR 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NOCApplicationController     : Application already exist for recognition application id REF-20250711-639CB6
2025-07-15T15:08:14.569+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:08:14.569+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250711-639CB6
2025-07-15T15:08:17.378+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:08:17.379+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250711-639CB6
2025-07-15T15:08:40.389+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:08:40.390+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:08:40.471+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T15:09:09.766+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T15:09:09.785+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:09:09.786+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number b43b5bc2-40ec-45b2-9ae6-6ca575ae54f8 has been updated successfully
2025-07-15T15:09:09.803+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T15:09:11.152+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T15:09:11.153+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T15:09:11.173+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T15:09:11.174+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 0 active complaints
2025-07-15T15:09:11.174+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-15T15:09:11.180+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Auto-assigned agent ad9f2ba3-898c-487f-a40e-893a10fe8477 to NCBSC application REF-********-47910A
2025-07-15T15:09:13.337+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Workflow response: {applicationData={courseDeliverySchedule=[{date=2024-01-16T00:00:00.000+00:00, topic=Data Types and Variables, hours=3, Id=2410539280862100, Uuid=0fcfbd1a-c5ad-4d7e-b30f-b62d89e26292}, {date=2024-01-15T00:00:00.000+00:00, topic=Introduction to Python, hours=4, Id=2410539281044900, Uuid=fefd66bc-fd5f-4df0-a8f5-c16cf123a9dc}], scopeOfAccreditations=[{fieldsOfLearningAccredited=Project Management, Id=2410539277387100, Uuid=e39e23f9-4b9a-4460-bc0f-7884dc8ac3aa}, {fieldsOfLearningAccredited=Information Technology, Id=2410539277609900, Uuid=11aa27a4-6e69-4204-afbb-8e51ce5e806d}], attachments=[], application={assignedAgent=ad9f2ba3-898c-487f-a40e-893a10fe8477, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, referenceNumber=REF-********-47910A, applicationNumber=APP-********-4A06F3, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, trainingNeedsAssessmentPurpose=Identify skill gaps in IT, trainingNeedsAssessmentSkillsNeedsAnalysis=Survey-based analysis of existing workforce skills, shortCourseDeliveryMode=Online, keyFacilitation=Experienced trainers with relevant certifications, assessmentType=Multiple-choice tests and project evaluations, certification=Certificate of completion, thirdPartyArrangements=Collaboration with ABC Institute, resources=Resources needed for the course, shortCourseEndorsement=Course Endorsement, dateSubmitted=2025-07-15T15:08:40.394624, applicationStatus=PENDING, applicationState=IN_PROCESSING, shortCourseInformation={title=Introduction to Sewing, type=Workshop, duration=2 weeks, fieldOfLearning=Computer Science, subFieldOfLearning=Conditional operations, level=NFQ, accreditingBody=HRDC, courseLearningTime=40, startDate=null, endDate=null, yearDueForReview=2025, targetPopulation=University students, entryRequirements=Basic computer literacy, Id=2410539278187800, Uuid=a4244646-106b-435b-9df3-f6c4ccaf2ff6}, courseContentAndDelivery={exitLevelOutcomes=Write basic programs in Python, learningOutcomesSummary=, shortCourseDeliveryMode=, shortCourseDeliveryType=, location=, Id=2410539278697800, Uuid=2555d33e-a984-424c-94e3-1793142d6f12}, processInstanceId=null, managerApprovedAt=null, renewalNotificationSent=false, renewalNotificationSentAt=null, deleted=false, Id=2410539275507000, Uuid=b43b5bc2-40ec-45b2-9ae6-6ca575ae54f8, CreatedAt=2025-07-15T15:08:40.421617, UpdatedAt=2025-07-15T15:09:11.178453, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}, documents=[{key=documents/pop/payment-proof-REF-20250710-A8A5F9.pdf, docName=null, docExt=null, identifier=b43b5bc2-40ec-45b2-9ae6-6ca575ae54f8, fileType=null, docSize=null, fileUrl=null, deleted=false, Id=2410568663669600, Uuid=f1558f1c-4592-49e9-afa9-17be8562017e, CreatedAt=2025-07-15T15:09:09.790702, UpdatedAt=2025-07-15T15:09:09.790702, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}], learningOutcomes=[{id=2410539279937100, assessmentCriteria=[{topic=, objective=, deliveryStrategy=, assessmentStrategy=, dateFrom=null, dateTo=null, hours=null, Id=2410539280437300, Uuid=dce2dfb4-a2a7-469f-b2a9-eb2dcdaa797f}], uuid=5b0176d8-4762-43ae-81e4-ff3003d29852, outcome=Understand Python syntax}], quotation={quoteRef=QUO-********-70310C, reference=b43b5bc2-40ec-45b2-9ae6-6ca575ae54f8, checklistItemsJson=null, totalAmount=5000.0, accepted=false, deleted=false, Id=2410539350174100, Uuid=56f7005f-d941-4453-8a65-01c87b915b0a, CreatedAt=2025-07-15T15:08:40.476619, UpdatedAt=2025-07-15T15:08:40.476619, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}}, processInstanceId=8fde6793-615f-11f0-aa8b-00155d4133f2, message=Process started and application data retrieved successfully, success=true}
2025-07-15T15:09:13.348+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.n.NCBSCApplicationService    : Successfully updated process instance ID 8fde6793-615f-11f0-aa8b-00155d4133f2 for application REF-********-47910A
2025-07-15T15:09:13.350+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-2] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Process instance ID 8fde6793-615f-11f0-aa8b-00155d4133f2 saved for application REF-********-47910A
2025-07-15T15:09:41.429+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T15:09:41.449+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:09:41.461+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-1C1E18 has been updated successfully
2025-07-15T15:09:54.532+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:09:54.532+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-20250711-639CB6
2025-07-15T15:10:00.008+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:10:00.*********
2025-07-15T15:10:00.008+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:10:00.********* to 2023-01-15T15:10:00.*********
2025-07-15T15:10:00.012+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:10:00.013+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:10:22.363+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:10:22.363+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-********-47910A
2025-07-15T15:10:35.879+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:10:35.880+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:10:35.884+05:30 ERROR 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NCBSCApplicationController   : Application already exist for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:11:08.747+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:11:14.022+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Application creation initiated for organization ID: bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:11:14.024+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : Checking ongoing application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7
2025-07-15T15:11:14.105+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NCBSCApplicationController   : New application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully with application number null
2025-07-15T15:12:13.839+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : NOC Application quote accept status initiated
2025-07-15T15:12:13.863+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:12:13.875+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.NCBSCApplicationController   : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number REF-********-52A863 has been updated successfully
2025-07-15T15:12:27.398+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Recognition Application pop submission initiated
2025-07-15T15:12:27.406+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:12:27.408+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.NCBSCApplicationController   : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 000508b3-be22-4cb2-8175-e1a152760c32 has been updated successfully
2025-07-15T15:12:27.419+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-07-15T15:12:28.788+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-07-15T15:12:28.789+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-07-15T15:12:28.799+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {}
2025-07-15T15:12:28.799+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent ad9f2ba3-898c-487f-a40e-893a10fe8477 with 0 active complaints
2025-07-15T15:12:28.800+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: ad9f2ba3-898c-487f-a40e-893a10fe8477
2025-07-15T15:12:28.804+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Auto-assigned agent ad9f2ba3-898c-487f-a40e-893a10fe8477 to NCBSC application REF-********-C5A12F
2025-07-15T15:12:31.460+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Workflow response: {applicationData={courseDeliverySchedule=[{date=2024-01-16T00:00:00.000+00:00, topic=Data Types and Variables, hours=3, Id=2410692922480800, Uuid=a2d2be33-2b7d-41d3-bfc0-c0c0506f348b}, {date=2024-01-15T00:00:00.000+00:00, topic=Introduction to Python, hours=4, Id=2410692922639200, Uuid=85fbc5e8-10b9-4c39-b90d-f78962793833}], scopeOfAccreditations=[{fieldsOfLearningAccredited=Information Technology, Id=2410692920504300, Uuid=6475217d-44c4-4d93-8bc1-764e02b6e5b0}, {fieldsOfLearningAccredited=Project Management, Id=2410692920677500, Uuid=b0ea19e4-10b3-40b0-8366-0e5b07629fa3}], attachments=[], application={assignedAgent=ad9f2ba3-898c-487f-a40e-893a10fe8477, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, referenceNumber=REF-********-C5A12F, applicationNumber=APP-********-740F44, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, trainingNeedsAssessmentPurpose=Identify skill gaps in IT, trainingNeedsAssessmentSkillsNeedsAnalysis=Survey-based analysis of existing workforce skills, shortCourseDeliveryMode=Online, keyFacilitation=Experienced trainers with relevant certifications, assessmentType=Multiple-choice tests and project evaluations, certification=Certificate of completion, thirdPartyArrangements=Collaboration with ABC Institute, resources=Resources needed for the course, shortCourseEndorsement=Course Endorsement, dateSubmitted=2025-07-15T15:11:14.042411, applicationStatus=PENDING, applicationState=IN_PROCESSING, shortCourseInformation={title=Introduction to Sewing, type=Workshop, duration=2 weeks, fieldOfLearning=Computer Science, subFieldOfLearning=Conditional operations, level=NFQ, accreditingBody=HRDC, courseLearningTime=40, startDate=null, endDate=null, yearDueForReview=2025, targetPopulation=University students, entryRequirements=Basic computer literacy, Id=2410692921066200, Uuid=248be18f-9fb6-47f2-a7f1-73dcdcd96e41}, courseContentAndDelivery={exitLevelOutcomes=Write basic programs in Python, learningOutcomesSummary=, shortCourseDeliveryMode=, shortCourseDeliveryType=, location=, Id=2410692921436600, Uuid=d766b2c5-36b4-4023-abc2-ecbf7dd241a8}, processInstanceId=null, managerApprovedAt=null, renewalNotificationSent=false, renewalNotificationSentAt=null, deleted=false, Id=2410692919726800, Uuid=000508b3-be22-4cb2-8175-e1a152760c32, CreatedAt=2025-07-15T15:11:14.04641, UpdatedAt=2025-07-15T15:12:28.803056, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}, documents=[{key=documents/pop/payment-proof-REF-20250710-A8A5F9.pdf, docName=null, docExt=null, identifier=000508b3-be22-4cb2-8175-e1a152760c32, fileType=null, docSize=null, fileUrl=null, deleted=false, Id=2410766284923800, Uuid=dcddd075-b451-4831-bb67-e920a14aef88, CreatedAt=2025-07-15T15:12:27.409705, UpdatedAt=2025-07-15T15:12:27.409705, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}], learningOutcomes=[{id=2410692921771600, assessmentCriteria=[{topic=, objective=, deliveryStrategy=, assessmentStrategy=, dateFrom=null, dateTo=null, hours=null, Id=2410692922104700, Uuid=0c873619-ca6d-4b72-85e7-9254110d7321}], uuid=95591b22-5171-421b-9b3b-94e4dd1054ec, outcome=Understand Python syntax}], quotation={quoteRef=QUO-********-EBC7B9, reference=000508b3-be22-4cb2-8175-e1a152760c32, checklistItemsJson=null, totalAmount=5000.0, accepted=true, deleted=false, Id=2410692984080800, Uuid=4aa198cc-946e-4cd2-b087-bf808823dca9, CreatedAt=2025-07-15T15:11:14.108412, UpdatedAt=2025-07-15T15:12:13.882923, CreatedBy=system-user, UpdatedBy=system-user, Deleted=false}}, processInstanceId=05a7735b-6160-11f0-aa8b-00155d4133f2, message=Process started and application data retrieved successfully, success=true}
2025-07-15T15:12:31.464+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.n.NCBSCApplicationService    : Successfully updated process instance ID 05a7735b-6160-11f0-aa8b-00155d4133f2 for application REF-********-C5A12F
2025-07-15T15:12:31.465+05:30  INFO 13672 --- [workplace-learning] [WorkflowAsync-3] b.o.h.w.w.s.ncbsc.NCBSCWorkflowService   : Process instance ID 05a7735b-6160-11f0-aa8b-00155d4133f2 saved for application REF-********-C5A12F
2025-07-15T15:13:01.389+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:13:01.389+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-********-52A863
2025-07-15T15:14:09.408+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.NOCApplicationController     : NOC Application creation initiated
2025-07-15T15:14:09.408+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.NOCApplicationController     : Checking ongoing application for recognition id REF-********-C5A12F
2025-07-15T15:14:09.464+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.NOCApplicationController     : New NOC application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 created successfully
2025-07-15T15:15:00.008+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:15:00.*********
2025-07-15T15:15:00.009+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:15:00.********* to 2023-01-15T15:15:00.*********
2025-07-15T15:15:00.016+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:15:00.016+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:15:49.992+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NOCApplicationController     : NOC Application quote accept status initiated
2025-07-15T15:15:50.000+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NOCApplicationController     : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:15:50.012+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.NOCApplicationController     : Quotation for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 35fab567-efe6-4450-aca5-3045f911ff12 has been updated successfully
2025-07-15T15:16:07.121+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : NOC Application pop submission initiated
2025-07-15T15:16:07.233+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : Application for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 updated successfully, reference number generated and application submitted
2025-07-15T15:16:07.250+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : POP document for company id bc0d3baa-639b-4df8-80f9-82cdeebd29d7 and application reference number 35fab567-efe6-4450-aca5-3045f911ff12 has been updated successfully
2025-07-15T15:16:07.399+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : Starting workflow process for NOC application with reference number: REF-********-12BADF
2025-07-15T15:16:07.483+05:30  INFO 13672 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.NOCApplicationController     : Workflow process initiated in background for application: REF-********-12BADF
2025-07-15T15:16:08.763+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:16:10.521+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.c.NOCApplicationController     : Initiating workflow process for application reference: REF-********-12BADF
2025-07-15T15:16:12.618+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.c.NOCApplicationController     : Workflow response received: status=null, message=Process started and application data retrieved successfully
2025-07-15T15:16:12.619+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.c.NOCApplicationController     : Workflow response keys: [applicationData, processInstanceId, message, success]
2025-07-15T15:16:12.619+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.c.NOCApplicationController     : Found process instance ID directly in response: 89ceeed3-6160-11f0-aa8b-00155d4133f2
2025-07-15T15:16:12.655+05:30  WARN 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.s.ncbsc.NOCApplicationService  : Error updating process instance ID using JPA: Could not commit JPA transaction
2025-07-15T15:16:12.665+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.s.ncbsc.NOCApplicationService  : Process instance ID 89ceeed3-6160-11f0-aa8b-00155d4133f2 updated for application REF-********-12BADF using native query
2025-07-15T15:16:12.665+05:30  INFO 13672 --- [workplace-learning] [Thread-7] b.o.h.w.w.c.NOCApplicationController     : Process instance ID 89ceeed3-6160-11f0-aa8b-00155d4133f2 saved for application REF-********-12BADF
2025-07-15T15:20:00.001+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:20:00.001705
2025-07-15T15:20:00.002+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:20:00.002706400 to 2023-01-15T15:20:00.002706400
2025-07-15T15:20:00.010+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:20:00.010+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:21:08.772+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:25:00.015+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:25:00.014366900
2025-07-15T15:25:00.017+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:25:00.016200700 to 2023-01-15T15:25:00.016200700
2025-07-15T15:25:00.034+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:25:00.035+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:26:08.788+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:26:08.961+05:30  INFO 13672 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T15:26:08.970+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T15:26:08.971+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752573368971, current=DOWN, previous=UP]
2025-07-15T15:26:08.976+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752573368976, current=UP, previous=DOWN]
2025-07-15T15:26:08.977+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T15:26:08.996+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.a.k.clients.producer.KafkaProducer     : [Producer clientId=workplace-learning-producer-1] Closing the Kafka producer with timeoutMillis = 30000 ms.
2025-07-15T15:26:08.996+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T15:26:09.014+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics scheduler closed
2025-07-15T15:26:09.015+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-07-15T15:26:09.016+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-07-15T15:26:09.017+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics reporters closed
2025-07-15T15:26:09.018+05:30  INFO 13672 --- [workplace-learning] [Thread-1] o.a.kafka.common.utils.AppInfoParser     : App info kafka.producer for workplace-learning-producer-1 unregistered
2025-07-15T15:26:09.064+05:30  INFO 13672 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T15:26:09.072+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T15:26:09.084+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T15:26:09.090+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T15:26:12.099+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T15:26:12.142+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T15:26:12.143+05:30  INFO 13672 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T15:26:12.605+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T15:26:12.994+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 13672 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T15:26:12.995+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T15:26:15.404+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T15:26:15.781+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 375 ms. Found 31 JPA repository interfaces.
2025-07-15T15:26:16.165+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T15:26:16.597+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T15:26:16.606+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T15:26:16.618+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T15:26:16.632+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T15:26:16.636+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T15:26:17.054+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-07-15T15:26:17.056+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-15T15:26:17.057+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-07-15T15:26:17.152+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-15T15:26:17.153+05:30  INFO 13672 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4147 ms
2025-07-15T15:26:17.415+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-07-15T15:26:17.549+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@445d0c19
2025-07-15T15:26:17.550+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-07-15T15:26:17.551+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-07-15T15:26:18.171+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15T15:26:18.181+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-15T15:26:18.227+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15T15:26:20.095+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15T15:26:20.952+05:30  INFO 13672 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T15:26:23.414+05:30  WARN 13672 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15T15:26:23.881+05:30  WARN 13672 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-07-15T15:26:24.155+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T15:26:26.343+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-07-15T15:26:38.657+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-07-15T15:26:44.650+05:30  INFO 13672 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-07-15T15:26:44.999+05:30  WARN 13672 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-15T15:26:45.372+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-15T15:26:45.411+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-07-15T15:26:45.413+05:30  INFO 13672 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:26:45.416+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-07-15T15:26:45.418+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-07-15T15:26:45.419+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-07-15T15:26:45.420+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-07-15T15:26:45.422+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-07-15T15:26:45.424+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-07-15T15:26:45.426+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-07-15T15:26:45.755+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-07-15T15:26:45.758+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-07-15T15:26:45.760+05:30  INFO 13672 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-15T15:26:45.762+05:30  INFO 13672 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1752573405762 with initial instances count: 5
2025-07-15T15:26:45.776+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752573405776, current=UP, previous=STARTING]
2025-07-15T15:26:45.779+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T15:26:45.831+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T15:26:45.837+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-07-15T15:26:45.841+05:30  WARN 13672 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-07-15T15:26:45.856+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-07-15T15:26:45.861+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-07-15T15:26:45.965+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 33.349 seconds (process running for 10756.799)
2025-07-15T15:26:45.984+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-15T15:30:00.001+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Starting daily NCBSC application renewal check at 2025-07-15T15:30:00.001408900
2025-07-15T15:30:00.001+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.n.NCBSCApplicationService    : Finding applications expiring in next 6 months. Approval date range: 2022-07-15T15:30:00.001408900 to 2023-01-15T15:30:00.001408900
2025-07-15T15:30:00.050+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Found 0 applications needing renewal notification
2025-07-15T15:30:00.050+05:30  INFO 13672 --- [workplace-learning] [scheduling-1] b.o.h.w.w.s.RenewalNotificationScheduler : Completed daily NCBSC application renewal check. Successfully processed: 0, Failed: 0
2025-07-15T15:31:45.438+05:30  INFO 13672 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-07-15T15:32:59.658+05:30  INFO 13672 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-07-15T15:32:59.660+05:30  INFO 13672 --- [workplace-learning] [Thread-8] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-07-15T15:32:59.661+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752573779661, current=DOWN, previous=UP]
2025-07-15T15:32:59.664+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1752573779664, current=UP, previous=DOWN]
2025-07-15T15:32:59.665+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-07-15T15:32:59.672+05:30  INFO 13672 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-07-15T15:32:59.705+05:30  INFO 13672 --- [workplace-learning] [Thread-8] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15T15:32:59.771+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-07-15T15:32:59.796+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-07-15T15:32:59.870+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-07-15T15:33:02.903+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-07-15T15:33:02.923+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-07-15T15:33:02.923+05:30  INFO 13672 --- [workplace-learning] [Thread-8] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-07-15T15:33:03.032+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T15:33:03.118+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 13672 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T15:33:03.119+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T15:33:04.224+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T15:33:04.308+05:30  WARN 13672 --- [workplace-learning] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: java.lang.NoClassDefFoundError: List
2025-07-15T15:33:04.331+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-15T15:33:04.366+05:30 ERROR 13672 --- [workplace-learning] [restartedMain] o.s.boot.SpringApplication               : Application run failed

java.lang.NoClassDefFoundError: List
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3578) ~[na:na]
	at java.base/java.lang.Class.privateGetPublicMethods(Class.java:3603) ~[na:na]
	at java.base/java.lang.Class.getMethods(Class.java:2185) ~[na:na]
	at org.springframework.data.util.ReactiveWrappers.usesReactiveType(ReactiveWrappers.java:141) ~[spring-data-commons-3.3.5.jar:3.3.5]
	at org.springframework.data.repository.core.support.AbstractRepositoryMetadata.isReactiveRepository(AbstractRepositoryMetadata.java:133) ~[spring-data-commons-3.3.5.jar:3.3.5]
	at org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport.useRepositoryConfiguration(RepositoryConfigurationExtensionSupport.java:343) ~[spring-data-commons-3.3.5.jar:3.3.5]
	at org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport.getRepositoryConfigurations(RepositoryConfigurationExtensionSupport.java:97) ~[spring-data-commons-3.3.5.jar:3.3.5]
	at org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn(RepositoryConfigurationDelegate.java:168) ~[spring-data-commons-3.3.5.jar:3.3.5]
	at org.springframework.boot.autoconfigure.data.AbstractRepositoryConfigurationSourceSupport.registerBeanDefinitions(AbstractRepositoryConfigurationSourceSupport.java:62) ~[spring-boot-autoconfigure-3.3.5.jar:3.3.5]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:376) ~[spring-context-6.1.14.jar:6.1.14]
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:986) ~[na:na]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:375) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:148) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:429) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352) ~[spring-boot-3.3.5.jar:3.3.5]
	at bw.org.hrdc.weblogic.workplacelearning.WorkplaceLearningApplication.main(WorkplaceLearningApplication.java:28) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
Caused by: java.lang.ClassNotFoundException: List
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	... 31 common frames omitted

2025-07-15T15:33:13.260+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-07-15T15:33:13.587+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 13672 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-07-15T15:33:13.590+05:30  INFO 13672 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-07-15T15:33:25.785+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15T15:33:30.087+05:30  INFO 13672 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4075 ms. Found 31 JPA repository interfaces.
2025-07-15T15:33:35.344+05:30  INFO 13672 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7f274aee-557c-39cf-a753-c8aa0338de55
2025-07-15T15:33:37.527+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T15:33:37.607+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T15:33:37.687+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-15T15:33:37.762+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-15T15:33:37.852+05:30  WARN 13672 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
